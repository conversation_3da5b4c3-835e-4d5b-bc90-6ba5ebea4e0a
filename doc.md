import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class SoundState {
final bool isPlaying;
final String? activatedBy;
final String? activatedAt;
final String? stoppedBy;
final String? stoppedAt;

SoundState({
required this.isPlaying,
this.activatedBy,
this.activatedAt,
this.stoppedBy,
this.stoppedAt,
});

factory SoundState.fromJson(Map<String, dynamic> json) {
return SoundState(
isPlaying: json['is_playing'] ?? false,
activatedBy: json['activated_by'],
activatedAt: json['activated_at'],
stoppedBy: json['stopped_by'],
stoppedAt: json['stopped_at'],
);
}
}

class DeviceData {
final String deviceId;
final double latitude;
final double longitude;
final String timestamp;
final String receivedAt;
final String? nickname;

DeviceData({
required this.deviceId,
required this.latitude,
required this.longitude,
required this.timestamp,
required this.receivedAt,
this.nickname,
});

factory DeviceData.fromJson(Map<String, dynamic> json) {
return DeviceData(
deviceId: json['device_id'],
latitude: json['latitude'].toDouble(),
longitude: json['longitude'].toDouble(),
timestamp: json['timestamp'],
receivedAt: json['received_at'],
nickname: json['nickname'],
);
}

Map<String, dynamic> toJson() {
return {
'device_id': deviceId,
'latitude': latitude,
'longitude': longitude,
'timestamp': timestamp,
'received_at': receivedAt,
'nickname': nickname,
};
}
}

class LocationService {
static const String \_apiUrl = 'https://pool.taliso.com.br/geotest.php';
static const String \_isOnlineKey = 'is_online';
static const String \_deviceIdKey = 'device_id';
static const String \_nicknameKey = 'device_nickname';
static const int \_updateInterval = 20000; // 20 segundos

static String? \_cachedDeviceId;
static String? \_cachedNickname;
static Timer? \_locationTimer;

// Callback para quando o estado do som mudar
static Function(SoundState)? onSoundStateChanged;

static Future<void> initDeviceId() async {
try {
SharedPreferences prefs = await SharedPreferences.getInstance();
String? savedId = prefs.getString(\_deviceIdKey);

      if (savedId != null) {
        _cachedDeviceId = savedId;
        return;
      }

      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      String deviceId;

      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceId = 'android_${androidInfo.id}';
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceId = 'ios_${iosInfo.identifierForVendor}';
      } else {
        deviceId = 'device_${DateTime.now().millisecondsSinceEpoch}';
      }

      await prefs.setString(_deviceIdKey, deviceId);
      _cachedDeviceId = deviceId;
    } catch (e) {
      print('Erro ao inicializar ID do dispositivo: $e');
      // Não tentar salvar um fallback aqui para não chamar prefs no background
    }

}

static Future<bool> checkPermissions() async {
bool serviceEnabled;
LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return false;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
      return false;
    }

    return true;

}

static Future<void> requestPermissions() async {
LocationPermission permission = await Geolocator.checkPermission();
if (permission == LocationPermission.denied) {
await Geolocator.requestPermission();
}
}

static Future<Position?> getCurrentLocation() async {
try {
bool hasPermission = await checkPermissions();
if (!hasPermission) {
return null;
}

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      return position;
    } catch (e) {
      print('Erro ao obter localização: $e');
      return null;
    }

}

static Future<String> getDeviceId() async {
if (\_cachedDeviceId != null) {
return \_cachedDeviceId!;
}

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? savedId = prefs.getString(_deviceIdKey);

      if (savedId != null) {
        _cachedDeviceId = savedId;
        return savedId;
      }

      // Gerar um ID único baseado no timestamp para evitar conflitos
      String fallbackId = 'device_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecondsSinceEpoch}';
      _cachedDeviceId = fallbackId;
      return fallbackId;

    } catch (e) {
      print('Erro ao obter ID do dispositivo no background: $e');
      // Gerar ID único mesmo em caso de erro
      String errorId = 'device_error_${DateTime.now().millisecondsSinceEpoch}';
      _cachedDeviceId = errorId;
      return errorId;
    }

}

static Future<String?> getNickname() async {
if (\_cachedNickname != null) {
return \_cachedNickname;
}

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? nickname = prefs.getString(_nicknameKey);
      _cachedNickname = nickname;
      return nickname;
    } catch (e) {
      print('Erro ao obter nickname: $e');
      return null;
    }

}

static Future<void> setNickname(String nickname) async {
try {
SharedPreferences prefs = await SharedPreferences.getInstance();
await prefs.setString(\_nicknameKey, nickname);
\_cachedNickname = nickname;

      // Atualiza o nickname no servidor
      String deviceId = await getDeviceId();
      await updateNicknameOnServer(deviceId, nickname);
    } catch (e) {
      print('Erro ao salvar nickname: $e');
    }

}

static Future<void> updateNicknameOnServer(String deviceId, String nickname) async {
try {
final response = await http.put(
Uri.parse(\_apiUrl),
headers: {
'Content-Type': 'application/json',
},
body: jsonEncode({
'device_id': deviceId,
'nickname': nickname,
}),
);

      if (response.statusCode == 200) {
        print('Nickname atualizado no servidor: $nickname');
      } else {
        print('Erro ao atualizar nickname no servidor: ${response.statusCode}');
      }
    } catch (e) {
      print('Erro ao atualizar nickname no servidor: $e');
    }

}

static Future<List<DeviceData>?> sendLocation(String deviceId, double latitude, double longitude) async {
try {
String? nickname = await getNickname();

      final response = await http.post(
        Uri.parse(_apiUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'device_id': deviceId,
          'latitude': latitude,
          'longitude': longitude,
          'timestamp': DateTime.now().toIso8601String(),
          'nickname': nickname,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['devices'] != null) {
          List<DeviceData> devices = [];
          for (var deviceJson in data['devices']) {
            devices.add(DeviceData.fromJson(deviceJson));
          }

          // Verificar estado do som
          if (data['sound_state'] != null && onSoundStateChanged != null) {
            SoundState soundState = SoundState.fromJson(data['sound_state']);
            onSoundStateChanged!(soundState);
          }

          return devices;
        }
      }
      return null;
    } catch (e) {
      print('Erro ao enviar localização: $e');
      return null;
    }

}

static Future<List<DeviceData>?> getAllDevices() async {
try {
final response = await http.get(Uri.parse(\_apiUrl));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['devices'] != null) {
          List<DeviceData> devices = [];
          for (var deviceJson in data['devices']) {
            devices.add(DeviceData.fromJson(deviceJson));
          }

          // Verificar estado do som
          if (data['sound_state'] != null && onSoundStateChanged != null) {
            SoundState soundState = SoundState.fromJson(data['sound_state']);
            onSoundStateChanged!(soundState);
          }

          return devices;
        }
      }
      return null;
    } catch (e) {
      print('Erro ao buscar dispositivos: $e');
      return null;
    }

}

static Future<void> setOnlineStatus(bool isOnline) async {
SharedPreferences prefs = await SharedPreferences.getInstance();
await prefs.setBool(\_isOnlineKey, isOnline);
}

static Future<bool> getOnlineStatus() async {
SharedPreferences prefs = await SharedPreferences.getInstance();
return prefs.getBool(\_isOnlineKey) ?? false;
}

static Future<void> startPeriodicLocationUpdates() async {
// Para qualquer timer existente
\_locationTimer?.cancel();

    // Inicia novo timer para enviar localização a cada 20 segundos
    _locationTimer = Timer.periodic(Duration(milliseconds: _updateInterval), (timer) async {
      try {
        Position? position = await getCurrentLocation();
        if (position != null) {
          String deviceId = await getDeviceId();
          await sendLocation(deviceId, position.latitude, position.longitude);
          print('Localização enviada periodicamente: ${position.latitude}, ${position.longitude}');
        }
      } catch (e) {
        print('Erro no envio periódico de localização: $e');
      }
    });

    print('Iniciado envio periódico de localização a cada ${_updateInterval / 1000} segundos');

}

static void stopPeriodicLocationUpdates() {
\_locationTimer?.cancel();
\_locationTimer = null;
print('Parado envio periódico de localização');
}

static Future<bool> stopSound() async {
try {
String? nickname = await getNickname();
String deviceId = await getDeviceId();

      // Criar identificação clara do dispositivo que parou
      String deviceInfo = nickname?.isNotEmpty == true ?
        '$nickname (${deviceId.substring(0, 8)}...)' :
        'Dispositivo ${deviceId.substring(0, 8)}...';

      final response = await http.post(
        Uri.parse('${_apiUrl}?action=sound'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'command': 'stop',
          'stopped_by': deviceInfo,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          print('Som parado com sucesso');

          // Atualizar callback se disponível
          if (data['state'] != null && onSoundStateChanged != null) {
            SoundState soundState = SoundState.fromJson(data['state']);
            onSoundStateChanged!(soundState);
          }

          return true;
        }
      }
      return false;
    } catch (e) {
      print('Erro ao parar som: $e');
      return false;
    }

}

static Future<SoundState?> getSoundState() async {
try {
final response = await http.get(Uri.parse(\_apiUrl));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['sound_state'] != null) {
          return SoundState.fromJson(data['sound_state']);
        }
      }
      return null;
    } catch (e) {
      print('Erro ao verificar estado do som: $e');
      return null;
    }

}
}

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:audioplayers/audioplayers.dart';
import '../services/location_service.dart';

class HomeScreen extends StatefulWidget {
@override
\_HomeScreenState createState() => \_HomeScreenState();
}

class \_HomeScreenState extends State<HomeScreen> {
bool \_isOnline = false;
Position? \_currentPosition;
String \_statusMessage = 'Aguardando...';
List<DeviceData> \_allDevices = [];
String \_deviceId = '';
String \_nickname = '';
StreamSubscription<Position>? \_positionStreamSubscription;
Timer? \_deviceUpdateTimer;
final TextEditingController \_nicknameController = TextEditingController();

// Controle de som
AudioPlayer \_audioPlayer = AudioPlayer();
bool \_isSoundPlaying = false;
SoundState? \_currentSoundState;

@override
void initState() {
super.initState();
\_initialize();
}

Future<void> \_initialize() async {
await \_loadDeviceId();
await \_loadNickname();
await \_getCurrentLocation();

    final service = FlutterBackgroundService();
    bool isRunning = await service.isRunning();

    setState(() {
      _isOnline = isRunning;
      _statusMessage = isRunning ? 'Online' : 'Offline';
    });

    // Configurar callback para mudanças no estado do som
    LocationService.onSoundStateChanged = _onSoundStateChanged;

    service.on('update').listen((event) {
      if(event == null) return;

      final devicesData = event['devices'];
      if(devicesData is List){
        setState(() {
          _allDevices = devicesData.map((d) => DeviceData.fromJson(d as Map<String, dynamic>)).toList();
        });
      }
    });

     service.on('updateNotification').listen((event) {
        // A notificação já é atualizada no background, podemos usar isso para atualizar a UI se quisermos.
     });

    _deviceUpdateTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (_isOnline) {
        _loadAllDevices();
      }
    });

}

void \_onSoundStateChanged(SoundState soundState) {
setState(() {
\_currentSoundState = soundState;
});

    if (soundState.isPlaying && !_isSoundPlaying) {
      _startPlayingSound();
    } else if (!soundState.isPlaying && _isSoundPlaying) {
      _stopPlayingSound();
    }

}

Future<void> \_startPlayingSound() async {
setState(() {
\_isSoundPlaying = true;
});

    // Mostrar notificação visual imediatamente
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('🚨 ALARME ATIVADO! ${_currentSoundState?.activatedBy ?? 'Alguém'} ativou o som!'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }

    // Tentar tocar som (opcional - funcionará se houver um arquivo de som válido)
    try {
      // Primeiro tentar som local
      await _audioPlayer.setSource(AssetSource('sounds/alarm.wav'));
      await _audioPlayer.setReleaseMode(ReleaseMode.loop);
      await _audioPlayer.resume();
      print('Som de alarme local iniciado');
    } catch (e) {
      print('Som local não disponível, tentando som online: $e');
      try {
        // Tentar som online como fallback
        await _audioPlayer.setSource(UrlSource('https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'));
        await _audioPlayer.setReleaseMode(ReleaseMode.loop);
        await _audioPlayer.resume();
        print('Som de alarme online iniciado');
      } catch (e2) {
        print('Nenhum som disponível, usando apenas alertas visuais: $e2');

        // Repetir notificação visual a cada 3 segundos enquanto o som estiver ativo
        Timer.periodic(const Duration(seconds: 3), (timer) {
          if (!_isSoundPlaying) {
            timer.cancel();
            return;
          }

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('🚨 ALARME AINDA ATIVO! Ativado por: ${_currentSoundState?.activatedBy ?? 'Desconhecido'}'),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 2),
              ),
            );
          }
        });
      }
    }

}

Future<void> \_stopPlayingSound() async {
try {
await \_audioPlayer.stop();
setState(() {
\_isSoundPlaying = false;
});
print('Som de alarme parado');
} catch (e) {
print('Erro ao parar som: $e');
}
}

Future<void> \_stopSoundOnAllDevices() async {
try {
bool success = await LocationService.stopSound();
if (success && mounted) {
ScaffoldMessenger.of(context).showSnackBar(
SnackBar(
content: Text('Som parado em todos os dispositivos!'),
backgroundColor: Colors.green,
),
);
} else if (mounted) {
ScaffoldMessenger.of(context).showSnackBar(
SnackBar(
content: Text('Erro ao parar som. Tente novamente.'),
backgroundColor: Colors.red,
),
);
}
} catch (e) {
print('Erro ao parar som: $e');
if (mounted) {
ScaffoldMessenger.of(context).showSnackBar(
SnackBar(
content: Text('Erro ao comunicar com servidor.'),
backgroundColor: Colors.red,
),
);
}
}
}

@override
void dispose() {
\_positionStreamSubscription?.cancel();
\_deviceUpdateTimer?.cancel();
\_nicknameController.dispose();
\_audioPlayer.dispose();
super.dispose();
}

Future<void> \_loadDeviceId() async {
String deviceId = await LocationService.getDeviceId();
setState(() {
\_deviceId = deviceId;
});
}

Future<void> \_loadNickname() async {
String? nickname = await LocationService.getNickname();
setState(() {
\_nickname = nickname ?? '';
\_nicknameController.text = nickname ?? '';
});
}

Future<void> \_saveNickname() async {
String newNickname = \_nicknameController.text.trim();
if (newNickname.isNotEmpty) {
await LocationService.setNickname(newNickname);
setState(() {
\_nickname = newNickname;
});
ScaffoldMessenger.of(context).showSnackBar(
SnackBar(content: Text('Apelido salvo: $newNickname')),
);
}
}

Future<void> \_loadAllDevices() async {
List<DeviceData>? devices = await LocationService.getAllDevices();
if (devices != null) {
if(mounted){
setState(() {
\_allDevices = devices;
});
}
}
}

Future<void> \_getCurrentLocation() async {
Position? position = await LocationService.getCurrentLocation();
if (position != null) {
if(mounted){
setState(() {
\_currentPosition = position;
});
}
}
}

void \_toggleOnlineStatus() async {
final service = FlutterBackgroundService();
var isRunning = await service.isRunning();

    if (isRunning) {
      service.invoke("stopService");
      _positionStreamSubscription?.cancel();
      LocationService.stopPeriodicLocationUpdates();
      await LocationService.setOnlineStatus(false);
    } else {
      // Pedir permissões primeiro
      var notificationStatus = await Permission.notification.request();
      if (!notificationStatus.isGranted) {
        print("Permissão de notificação negada.");
        return;
      }

      await LocationService.requestPermissions();
      bool hasLocationPermission = await LocationService.checkPermissions();
      if (!hasLocationPermission) {
        print("Permissão de localização negada.");
        return;
      }

      await service.startService();
      await LocationService.setOnlineStatus(true);

      // Inicia o envio periódico de localização a cada 20 segundos
      await LocationService.startPeriodicLocationUpdates();

      // Mantém o stream de posição apenas para atualizar a UI
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10, // Atualiza a cada 10 metros
        )
      ).listen((Position position) {
        if(mounted){
          setState(() {
            _currentPosition = position;
          });
        }
      });
    }

    setState(() {
      _isOnline = !isRunning;
      _statusMessage = !isRunning ? 'Online' : 'Offline';
    });

}

@override
Widget build(BuildContext context) {
return Scaffold(
appBar: AppBar(
title: const Text('GeoSec App'),
backgroundColor: \_isOnline ? Colors.green : Colors.red,
),
body: SafeArea(
child: LayoutBuilder(
builder: (context, constraints) {
return SingleChildScrollView(
padding: EdgeInsets.only(
left: 20,
right: 20,
top: 20,
bottom: MediaQuery.of(context).viewInsets.bottom + 20,
),
child: ConstrainedBox(
constraints: BoxConstraints(
minHeight: constraints.maxHeight,
),
child: IntrinsicHeight(
child: Column(
mainAxisAlignment: MainAxisAlignment.center,
children: [
Icon(
\_isOnline ? Icons.radio_button_checked : Icons.radio_button_off,
size: 80,
color: \_isOnline ? Colors.green : Colors.red,
),
const SizedBox(height: 20),
Text(
'Status: $_statusMessage',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: _isOnline ? Colors.green : Colors.red,
                        ),
                      ),
                      const SizedBox(height: 40),
                      if (_deviceId.isNotEmpty) ...[
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              children: [
                                const Text(
                                  'ID do Dispositivo',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Text(
                                  _deviceId,
                                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              children: [
                                const Text(
                                  'Apelido do Dispositivo',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Row(
                                  children: [
                                    Expanded(
                                      child: TextField(
                                        controller: _nicknameController,
                                        decoration: InputDecoration(
                                          hintText: 'Digite um apelido...',
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                        ),
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    ElevatedButton(
                                      onPressed: _saveNickname,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue,
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                      ),
                                      child: const Text('Salvar'),
                                    ),
                                  ],
                                ),
                                if (_nickname.isNotEmpty) ...[
                                  const SizedBox(height: 10),
                                  Text(
                                    'Apelido atual: $_nickname',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.green,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              if (_currentPosition != null) ...[
                                Card(
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Column(
                                      children: [
                                        const Text(
                                          'Coordenadas Atuais',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        Text(
                                          'Latitude: ${_currentPosition!.latitude.toStringAsFixed(6)}',
                                          style: const TextStyle(fontSize: 16),
                                        ),
                                        Text(
                                          'Longitude: ${_currentPosition!.longitude.toStringAsFixed(6)}',
                                          style: const TextStyle(fontSize: 16),
                                        ),
                                        Text(
                                          'Precisão: ${_currentPosition!.accuracy.toStringAsFixed(2)}m',
                                          style: const TextStyle(fontSize: 14, color: Colors.grey),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 20),
                              ],
                              if (_allDevices.isNotEmpty) ...[
                                Card(
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Column(
                                      children: [
                                        Text(
                                          'Dispositivos Conectados (${\_allDevices.length})',
style: const TextStyle(
fontSize: 18,
fontWeight: FontWeight.bold,
),
),
const SizedBox(height: 10),
SizedBox(
height: 200,
child: ListView.builder(
itemCount: \_allDevices.length,
itemBuilder: (context, index) {
DeviceData device = \_allDevices[index];
bool isCurrentDevice = device.deviceId == \_deviceId;
return Card(
color: isCurrentDevice ? Colors.green.shade100 : null,
child: ListTile(
leading: Icon(
isCurrentDevice ? Icons.smartphone : Icons.devices_other,
color: isCurrentDevice ? Colors.green : Colors.grey,
),
title: Text(
isCurrentDevice ? 'Este Dispositivo' : 'Dispositivo',
style: TextStyle(
fontWeight: isCurrentDevice ? FontWeight.bold : FontWeight.normal,
),
),
subtitle: Column(
crossAxisAlignment: CrossAxisAlignment.start,
children: [
Text(
device.nickname != null && device.nickname!.isNotEmpty
? '${device.nickname} (${device.deviceId.substring(0, 15)}...)'
: 'ID: ${device.deviceId.substring(0, 15)}...',
overflow: TextOverflow.ellipsis,
style: TextStyle(
fontWeight: device.nickname != null && device.nickname!.isNotEmpty
? FontWeight.bold
: FontWeight.normal,
),
),
Text('Lat: ${device.latitude.toStringAsFixed(4)}, Lng: ${device.longitude.toStringAsFixed(4)}'),
Text('Atualizado: ${device.receivedAt.split(' ')[1]}'),
],
),
),
);
},
),
),
],
),
),
),
],
],
),
),
),
const SizedBox(height: 20),
ElevatedButton(
onPressed: \_toggleOnlineStatus,
style: ElevatedButton.styleFrom(
backgroundColor: \_isOnline ? Colors.red : Colors.green,
foregroundColor: Colors.white,
padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
),
child: Text(
\_isOnline ? 'Ficar Offline' : 'Ficar Online',
style: const TextStyle(fontSize: 18),
),
),
const SizedBox(height: 20),

                      // Seção de controle de som
                      if (_currentSoundState != null && _currentSoundState!.isPlaying) ...[
                        Card(
                          color: Colors.red.shade50,
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.volume_up,
                                  size: 48,
                                  color: Colors.red,
                                ),
                                const SizedBox(height: 10),
                                Text(
                                  '🚨 ALARME ATIVO!',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                                const SizedBox(height: 5),
                                Text(
                                  'Ativado por: ${_currentSoundState!.activatedBy ?? 'Desconhecido'}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.red.shade700,
                                  ),
                                ),
                                const SizedBox(height: 15),
                                ElevatedButton(
                                  onPressed: _stopSoundOnAllDevices,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                                  ),
                                  child: const Text(
                                    '🛑 PARAR SOM EM TODOS OS DISPOSITIVOS',
                                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],

                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton(
                            onPressed: _getCurrentLocation,
                            child: const Text('Atualizar Posição'),
                          ),
                          ElevatedButton(
                            onPressed: _loadAllDevices,
                            child: const Text('Atualizar Lista'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );

}
}

# Implementações Necessárias para LocationService

## Arquivo Ausente: lib/services/location_service.dart

O projeto referencia `LocationService` mas o arquivo não existe. Aqui está a implementação necessária:

```dart
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocationService {
  static const String _baseUrl = 'SEU_SERVIDOR_AQUI'; // Configurar URL do servidor
  static const String _apiKey = 'SUA_API_KEY_AQUI';   // Configurar API Key

  static Function(SoundState)? onSoundStateChanged;
  static Timer? _periodicTimer;

  // Inicialização do serviço em segundo plano
  static Future<void> initializeService() async {
    final service = FlutterBackgroundService();

    await service.configure(
      androidConfiguration: AndroidConfiguration(
        onStart: onStart,
        autoStart: false,
        isForegroundMode: true,
        notificationChannelId: 'geo_tracker_channel',
        initialNotificationTitle: 'GeoTracker',
        initialNotificationContent: 'Rastreamento ativo',
        foregroundServiceNotificationId: 888,
      ),
      iosConfiguration: IosConfiguration(
        autoStart: false,
        onForeground: onStart,
        onBackground: onIosBackground,
      ),
    );
  }

  // Callback para início do serviço
  @pragma('vm:entry-point')
  static void onStart(ServiceInstance service) async {
    DartPluginRegistrant.ensureInitialized();

    if (service is AndroidServiceInstance) {
      service.on('setAsForeground').listen((event) {
        service.setAsForegroundService();
      });
      service.on('setAsBackground').listen((event) {
        service.setAsBackgroundService();
      });
    }

    service.on('stopService').listen((event) {
      service.stopSelf();
    });

    // Timer para envio periódico de localização
    Timer.periodic(const Duration(seconds: 20), (timer) async {
      if (!(await service.isRunning())) {
        timer.cancel();
        return;
      }

      try {
        Position? position = await getCurrentLocation();
        if (position != null) {
          await enviarLocalizacaoParaServidor(position);

          // Atualizar notificação
          await atualizarNotificacao(
            'Localização enviada',
            'Lat: ${position.latitude.toStringAsFixed(4)}, Lng: ${position.longitude.toStringAsFixed(4)}'
          );
        }
      } catch (e) {
        print('Erro ao enviar localização: $e');
      }
    });
  }

  // Callback para iOS background
  @pragma('vm:entry-point')
  static bool onIosBackground(ServiceInstance service) {
    WidgetsFlutterBinding.ensureInitialized();
    return true;
  }

  // Solicitar permissões necessárias
  static Future<void> requestPermissions() async {
    // Permissão de localização
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('Permissões de localização negadas permanentemente');
    }

    // Para segundo plano, precisa de "always"
    if (permission == LocationPermission.whileInUse) {
      permission = await Geolocator.requestPermission();
    }

    // Outras permissões
    await Permission.notification.request();

    if (Platform.isAndroid) {
      await Permission.ignoreBatteryOptimizations.request();
    }
  }

  // Verificar se tem permissões
  static Future<bool> checkPermissions() async {
    LocationPermission permission = await Geolocator.checkPermission();
    return permission == LocationPermission.whileInUse ||
           permission == LocationPermission.always;
  }

  // Obter localização atual
  static Future<Position?> getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return null;
      }

      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: Duration(seconds: 10),
      );
    } catch (e) {
      print('Erro ao obter localização: $e');
      return null;
    }
  }

  // Enviar localização para servidor
  static Future<void> enviarLocalizacaoParaServidor(Position position) async {
    try {
      String deviceId = await getDeviceId();
      String? nickname = await getNickname();

      final response = await http.post(
        Uri.parse('$_baseUrl/location'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode({
          'deviceId': deviceId,
          'nickname': nickname,
          'latitude': position.latitude,
          'longitude': position.longitude,
          'accuracy': position.accuracy,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      ).timeout(Duration(seconds: 10));

      if (response.statusCode != 200) {
        throw HttpException('Erro no servidor: ${response.statusCode}');
      }
    } catch (e) {
      print('Erro ao enviar localização: $e');
      // Salvar localmente para tentar depois
      await salvarLocalizacaoLocalmente(position);
    }
  }

  // Salvar localização localmente quando não conseguir enviar
  static Future<void> salvarLocalizacaoLocalmente(Position position) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      List<String> localizacoesPendentes = prefs.getStringList('localizacoes_pendentes') ?? [];

      String localizacaoJson = json.encode({
        'latitude': position.latitude,
        'longitude': position.longitude,
        'accuracy': position.accuracy,
        'timestamp': DateTime.now().toIso8601String(),
      });

      localizacoesPendentes.add(localizacaoJson);

      // Manter apenas últimas 100 localizações
      if (localizacoesPendentes.length > 100) {
        localizacoesPendentes = localizacoesPendentes.sublist(localizacoesPendentes.length - 100);
      }

      await prefs.setStringList('localizacoes_pendentes', localizacoesPendentes);
    } catch (e) {
      print('Erro ao salvar localização localmente: $e');
    }
  }

  // Obter ID único do dispositivo
  static Future<String> getDeviceId() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? deviceId = prefs.getString('device_id');

    if (deviceId == null) {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceId = '${androidInfo.model}_${androidInfo.id}';
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceId = '${iosInfo.model}_${iosInfo.identifierForVendor}';
      } else {
        deviceId = 'unknown_${DateTime.now().millisecondsSinceEpoch}';
      }

      await prefs.setString('device_id', deviceId);
    }

    return deviceId;
  }

  // Gerenciar apelido do dispositivo
  static Future<String?> getNickname() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('nickname');
  }

  static Future<void> setNickname(String nickname) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('nickname', nickname);
  }

  // Gerenciar status online/offline
  static Future<void> setOnlineStatus(bool isOnline) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('is_online', isOnline);
  }

  static Future<bool> getOnlineStatus() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool('is_online') ?? false;
  }

  // Iniciar/parar atualizações periódicas
  static Future<void> startPeriodicLocationUpdates() async {
    await setOnlineStatus(true);
  }

  static Future<void> stopPeriodicLocationUpdates() async {
    _periodicTimer?.cancel();
    await setOnlineStatus(false);
  }

  // Buscar todos os dispositivos conectados
  static Future<List<DeviceData>?> getAllDevices() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/devices'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
        },
      ).timeout(Duration(seconds: 10));

      if (response.statusCode == 200) {
        List<dynamic> devicesJson = json.decode(response.body);
        return devicesJson.map((d) => DeviceData.fromJson(d)).toList();
      }
    } catch (e) {
      print('Erro ao buscar dispositivos: $e');
    }
    return null;
  }

  // Funcionalidades de som (alarme)
  static Future<bool> stopSound() async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/stop-sound'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode({
          'deviceId': await getDeviceId(),
        }),
      ).timeout(Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      print('Erro ao parar som: $e');
      return false;
    }
  }

  // Atualizar notificação do serviço
  static Future<void> atualizarNotificacao(String title, String content) async {
    if (Platform.isAndroid) {
      FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
          FlutterLocalNotificationsPlugin();

      const AndroidNotificationDetails androidNotificationDetails =
          AndroidNotificationDetails(
        'geo_tracker_channel',
        'GeoTracker Localização',
        channelDescription: 'Notificações do rastreamento de localização',
        importance: Importance.low,
        priority: Priority.low,
        ongoing: true,
        autoCancel: false,
      );

      const NotificationDetails notificationDetails =
          NotificationDetails(android: androidNotificationDetails);

      await flutterLocalNotificationsPlugin.show(
        888,
        title,
        content,
        notificationDetails,
      );
    }
  }
}

// Classe para dados do dispositivo
class DeviceData {
  final String deviceId;
  final String? nickname;
  final double latitude;
  final double longitude;
  final String receivedAt;

  DeviceData({
    required this.deviceId,
    this.nickname,
    required this.latitude,
    required this.longitude,
    required this.receivedAt,
  });

  factory DeviceData.fromJson(Map<String, dynamic> json) {
    return DeviceData(
      deviceId: json['deviceId'] ?? '',
      nickname: json['nickname'],
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      receivedAt: json['receivedAt'] ?? '',
    );
  }
}

// Classe para estado do som
class SoundState {
  final bool isPlaying;
  final String? activatedBy;

  SoundState({
    required this.isPlaying,
    this.activatedBy,
  });

  factory SoundState.fromJson(Map<String, dynamic> json) {
    return SoundState(
      isPlaying: json['isPlaying'] ?? false,
      activatedBy: json['activatedBy'],
    );
  }
}
```

## Configuração Adicional Necessária no main.dart

```dart
import 'package:flutter/material.dart';
import 'services/location_service.dart';
import 'screens/home_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Inicializar serviço de localização
  await LocationService.initializeService();

  runApp(const MyApp());
}

// ... resto do código permanece igual
```

## Configuração do Info.plist para iOS

Adicione ao arquivo `ios/Runner/Info.plist`:

```xml
<!-- Antes da tag </dict> final -->
<key>NSLocationWhenInUseUsageDescription</key>
<string>Este aplicativo precisa acessar sua localização para rastreamento de segurança em tempo real.</string>

<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>Este aplicativo precisa acessar sua localização continuamente, mesmo em segundo plano, para garantir o rastreamento de segurança.</string>

<key>NSLocationAlwaysUsageDescription</key>
<string>Este aplicativo precisa acessar sua localização mesmo quando não está em uso para manter o rastreamento de segurança ativo.</string>

<key>UIBackgroundModes</key>
<array>
    <string>location</string>
    <string>background-processing</string>
    <string>fetch</string>
</array>

<key>BGTaskSchedulerPermittedIdentifiers</key>
<array>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER).refresh</string>
</array>
```

## Configuração do Servidor Backend

Para funcionamento completo, você precisa de um servidor que aceite:

### Endpoints Necessários:

- `POST /location` - Receber dados de localização
- `GET /devices` - Listar dispositivos conectados
- `POST /stop-sound` - Parar alarme em todos dispositivos

### Exemplo de resposta `/devices`:

```json
[
  {
    "deviceId": "samsung_abc123",
    "nickname": "João - Celular",
    "latitude": -23.5505,
    "longitude": -46.6333,
    "receivedAt": "2025-06-30 10:30:45"
  }
]
```

## Variáveis de Configuração

No arquivo `location_service.dart`, configure:

- `_baseUrl`: URL do seu servidor backend
- `_apiKey`: Chave de autenticação da API

## Teste da Implementação

1. **Teste de Permissões**: Verificar se todas as permissões são solicitadas
2. **Teste de Segundo Plano**: App minimizado deve continuar enviando localização
3. **Teste de Reconexão**: Testar comportamento sem internet
4. **Teste de Bateria**: Verificar otimizações em dispositivos reais
5. **Teste iOS vs Android**: Comportamentos diferentes entre plataformas

# Configuração de Localização em Segundo Plano - GeoTracker App

## Dependências Utilizadas

```yaml
dependencies:
  flutter_background_service: ^5.1.0 # Serviço em segundo plano
  geolocator: ^10.1.0 # Captura de localização GPS
  permission_handler: ^11.0.1 # Gerenciamento de permissões
  flutter_local_notifications: ^17.0.0 # Notificações locais
  device_info_plus: ^9.1.1 # Informações do dispositivo
  shared_preferences: ^2.2.2 # Armazenamento local
  http: ^1.1.2 # Requisições HTTP
  audioplayers: ^6.0.0 # Reprodução de sons
```

## Configurações Android

### AndroidManifest.xml

```xml
<!-- Permissões necessárias -->
<uses-permission android:name="android.permission.INTERNET"/>
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.WAKE_LOCK"/>
<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION"/>

<!-- Configuração do serviço em segundo plano -->
<service
    android:name="id.flutter.flutter_background_service.BackgroundService"
    android:foregroundServiceType="location"
    android:exported="true"
    tools:node="merge"
    tools:replace="android:foregroundServiceType,android:exported" />
```

### Permissões Obrigatórias para Localização em Segundo Plano:

1. **ACCESS_FINE_LOCATION**: Localização precisa via GPS
2. **ACCESS_COARSE_LOCATION**: Localização aproximada via rede
3. **FOREGROUND_SERVICE**: Execução de serviços em primeiro plano
4. **FOREGROUND_SERVICE_LOCATION**: Específico para serviços de localização (Android 14+)
5. **WAKE_LOCK**: Manter dispositivo ativo para capturar localização
6. **POST_NOTIFICATIONS**: Exibir notificações do serviço

## Configurações iOS

### Info.plist - Permissões Necessárias

**ATENÇÃO**: O Info.plist atual está INCOMPLETO para localização em segundo plano.

Adicione as seguintes chaves ao `ios/Runner/Info.plist`:

```xml
<!-- Permissões de localização -->
<key>NSLocationWhenInUseUsageDescription</key>
<string>Este aplicativo precisa acessar sua localização para rastreamento de segurança.</string>

<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>Este aplicativo precisa acessar sua localização continuamente para rastreamento em segundo plano.</string>

<key>NSLocationAlwaysUsageDescription</key>
<string>Este aplicativo precisa acessar sua localização mesmo quando fechado para segurança.</string>

<!-- Modo de execução em segundo plano -->
<key>UIBackgroundModes</key>
<array>
    <string>location</string>
    <string>background-processing</string>
    <string>fetch</string>
</array>
```

## Implementação no Código

### Fluxo de Funcionamento

1. **Inicialização do App**:

   - Carrega ID do dispositivo
   - Carrega apelido do dispositivo
   - Verifica status do serviço em segundo plano
   - Configura callbacks de mudança de estado

2. **Ativação do Modo Online**:

   ```dart
   // Solicita permissões
   await Permission.notification.request();
   await LocationService.requestPermissions();

   // Inicia serviço em segundo plano
   await service.startService();
   await LocationService.setOnlineStatus(true);

   // Inicia envio periódico de localização
   await LocationService.startPeriodicLocationUpdates();
   ```

3. **Captura de Localização**:
   ```dart
   Geolocator.getPositionStream(
     locationSettings: const LocationSettings(
       accuracy: LocationAccuracy.high,
       distanceFilter: 10, // Atualiza a cada 10 metros
     )
   )
   ```

### Configurações de Localização

- **Precisão**: LocationAccuracy.high (GPS)
- **Filtro de Distância**: 10 metros
- **Intervalo de Envio**: 20 segundos
- **Funcionamento em Segundo Plano**: Contínuo

## Tratamento de Permissões

### Android (API 23+)

```dart
// Permissões solicitadas automaticamente
await LocationService.requestPermissions();
bool hasPermission = await LocationService.checkPermissions();
```

### iOS

```dart
// Primeiro solicita "quando em uso"
// Depois solicita "sempre" para segundo plano
await Permission.locationWhenInUse.request();
await Permission.locationAlways.request();
```

## Otimizações de Bateria

### Android

- Usa `FOREGROUND_SERVICE` para evitar morte do processo
- Notificação persistente mantém serviço ativo
- `WAKE_LOCK` para captura mesmo com tela desligada

### iOS

- `UIBackgroundModes` com `location` mantém app ativo
- Sistema iOS gerencia automaticamente economia de bateria
- Reduz frequência de updates quando bateria baixa

## Funcionamento do Serviço em Segundo Plano

1. **Notificação Persistente**: Mantém usuário informado sobre o rastreamento
2. **Envio Automático**: A cada 20 segundos envia localização para servidor
3. **Reconexão Automática**: Se perder conexão, tenta reconectar
4. **Armazenamento Local**: Salva dados localmente se não conseguir enviar

## Tratamento de Estados do App

- **Primeiro Plano**: Atualização em tempo real da interface
- **Segundo Plano**: Serviço continua enviando localização
- **App Fechado**: Serviço permanece ativo (Android) / Limitado (iOS)
- **Reinicialização**: Serviço pode ser configurado para auto-iniciar

## Limitações por Plataforma

### Android

- ✅ Funciona indefinidamente em segundo plano
- ✅ Notificação obrigatória mas funcional
- ⚠️ Usuário pode desabilitar nas configurações do sistema

### iOS

- ⚠️ Sistema pode limitar tempo de execução em segundo plano
- ⚠️ Requer "Sempre" permitir localização
- ⚠️ iOS pode pausar app após algumas horas sem uso
- ✅ Funciona bem quando app está em uso ou recém-minimizado

## Resolução de Problemas Comuns

### "Localização não funciona em segundo plano"

1. Verificar se permissões estão concedidas
2. Verificar se serviço em segundo plano está rodando
3. Verificar configurações de economia de bateria
4. No iOS, verificar se "Sempre" está permitido

### "App para de enviar localização"

1. Android: Verificar se app não foi otimizado pelo sistema
2. iOS: Verificar se background refresh está habilitado
3. Verificar conectividade com servidor
4. Verificar se bateria não está muito baixa

### "Permissões negadas"

1. Explicar claramente o motivo da necessidade
2. Direcionar usuário para configurações se necessário
3. Oferecer funcionalidade limitada se possível

## Conformidade com Políticas das Lojas

### Google Play Store

- ✅ Justificativa clara para uso de localização
- ✅ Permissões apropriadas declaradas
- ✅ Funcionalidade principal relacionada à localização

### Apple App Store

- ✅ Descrição clara no Info.plist
- ✅ Funcionalidade justifica acesso contínuo
- ✅ Interface permite controle pelo usuário

## Próximos Passos para Implementação Completa

1.  **Criar LocationService**: Implementar arquivo `lib/services/location_service.dart`
2.  **Configurar iOS**: Adicionar permissões completas no Info.plist
3.  **Testar Exaustivamente**: Em diferentes versões de iOS/Android
4.  **Otimizar Bateria**: Implementar lógica adaptativa baseada em bateria
5.  **Melhorar UX**: Explicações claras sobre necessidade das permissões
    <manifest xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION"/>

        <application
            android:label="geo_tracker_app"
            android:name="${applicationName}"
            android:icon="@mipmap/ic_launcher">
            <activity
                android:name=".MainActivity"
                android:exported="true"
                android:launchMode="singleTop"
                android:taskAffinity=""
                android:theme="@style/LaunchTheme"
                android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
                android:hardwareAccelerated="true"
                android:windowSoftInputMode="adjustResize">
                <!-- Specifies an Android theme to apply to this Activity as soon as
                     the Android process has started. This theme is visible to the user
                     while the Flutter UI initializes. After that, this theme continues
                     to determine the Window background behind the Flutter UI. -->
                <meta-data
                  android:name="io.flutter.embedding.android.NormalTheme"
                  android:resource="@style/NormalTheme"
                  />
                <intent-filter>
                    <action android:name="android.intent.action.MAIN"/>
                    <category android:name="android.intent.category.LAUNCHER"/>
                </intent-filter>
            </activity>
            <!-- Don't delete the meta-data below.
                 This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
            <meta-data
                android:name="flutterEmbedding"
                android:value="2" />

            <!-- Garante que o serviço do plugin declare o tipo de serviço em 1.º plano (Android 14+) -->
            <service
                android:name="id.flutter.flutter_background_service.BackgroundService"
                android:foregroundServiceType="location"
                android:exported="true"
                tools:node="merge"
                tools:replace="android:foregroundServiceType,android:exported" />

        </application>
        <!-- Required to query activities that can process text, see:
             https://developer.android.com/training/package-visibility and
             https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

             In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
        <queries>
            <intent>
                <action android:name="android.intent.action.PROCESS_TEXT"/>
                <data android:mimeType="text/plain"/>
            </intent>
        </queries>

    </manifest>
