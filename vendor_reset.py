from datetime import datetime, timedelta, timezone

import firebase_admin
from firebase_admin import credentials, firestore

cred = credentials.Certificate(**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)
firebase_admin.initialize_app(cred)

db = firestore.client()


now = datetime.now(timezone.utc)

cinco_minutos_atras = now - timedelta(minutes=5)


entregadores_ref = db.collection("delivery_men_status")
docs = entregadores_ref.stream()


entregadores_online = []

for doc in docs:
    data = doc.to_dict()
    last_active = data.get("lastActive")
    if last_active:
        if isinstance(last_active, datetime):
            last_active_dt = last_active
        else:
            try:
                last_active_dt = datetime.strptime(
                    last_active, "%d de %B de %Y às %H:%M:%S UTC-3")
                last_active_dt = last_active_dt.replace(
                    tzinfo=timezone(timedelta(minutes=-5)))
                last_active_dt = last_active_dt.astimezone(
                    timezone.utc)
            except Exception as e:
                print(f"Erro ao converter timestamp: {e}")
                continue

        if last_active_dt >= cinco_minutos_atras:
            entregadores_online.append(doc.id)
print("Total de entregadores online:", len(entregadores_online))

print("Entregadores online nos últimos 5 minutos:", entregadores_online)
