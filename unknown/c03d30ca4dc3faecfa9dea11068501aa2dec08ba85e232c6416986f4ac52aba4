import 'package:flutter/material.dart';

class OrderProgressIndicator extends StatelessWidget {
  final double progress;
  final int timeLeft;

  const OrderProgressIndicator(
      {super.key, required this.progress, required this.timeLeft});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        LinearProgressIndicator(
          borderRadius: BorderRadius.circular(10),
          minHeight: 10,
          value: progress,
          valueColor: const AlwaysStoppedAnimation<Color>(Colors.blueAccent),
          backgroundColor: Colors.grey[300],
        ),
        Text(
          "$timeLeft s",
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }
}
