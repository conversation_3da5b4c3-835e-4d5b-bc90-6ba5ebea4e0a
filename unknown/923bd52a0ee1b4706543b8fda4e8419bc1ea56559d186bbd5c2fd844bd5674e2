import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:emartdriver/services/helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class BankDetailsScreen extends StatefulWidget {
  const BankDetailsScreen({Key? key}) : super(key: key);

  @override
  State<BankDetailsScreen> createState() => _BankDetailsScreenState();
}

enum TipoPagamento { pix, contaEfi }

enum TipoDocumento { cpf, cnpj }

class _BankDetailsScreenState extends State<BankDetailsScreen> {
  User? user;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isSaving = false;

  TipoPagamento _tipoPagamentoSelecionado = TipoPagamento.pix;
  TipoDocumento _tipoDocumentoSelecionado = TipoDocumento.cpf;

  // Máscaras para documentos
  final _cpfMask = MaskTextInputFormatter(
    mask: '###.###.###-##',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskAutoCompletionType.lazy,
  );

  final _cnpjMask = MaskTextInputFormatter(
    mask: '##.###.###/####-##',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskAutoCompletionType.lazy,
  );

  // Controllers para os campos
  final TextEditingController _pixKeyController = TextEditingController();
  final TextEditingController _contaEfiController = TextEditingController();
  final TextEditingController _documentoEfiController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          isDarkMode(context) ? const Color(DARK_VIEWBG_COLOR) : Colors.white,
      body: _isLoading ? _buildLoadingState() : _buildForm(),
    );
  }

  @override
  void dispose() {
    _pixKeyController.dispose();
    _contaEfiController.dispose();
    _documentoEfiController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Widget _buildForm() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xff425799).withValues(alpha: 0.1),
            Colors.white,
            Colors.white,
          ],
        ),
      ),
      child: Column(
        children: [
          // Conteúdo rolável
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Tipo de Pagamento',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[800],
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildTipoPagamentoCard(
                            tipo: TipoPagamento.pix,
                            titulo: 'PIX',
                            subtitulo: 'Taxa: 1.49% por saque',
                            icone: Icons.pix,
                            isSelected:
                                _tipoPagamentoSelecionado == TipoPagamento.pix,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildTipoPagamentoCard(
                            tipo: TipoPagamento.contaEfi,
                            titulo: 'Conta EFI',
                            subtitulo: 'Sem taxa de saque',
                            icone: Icons.account_balance,
                            isSelected: _tipoPagamentoSelecionado ==
                                TipoPagamento.contaEfi,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    if (_tipoPagamentoSelecionado == TipoPagamento.pix) ...[
                      _buildPixSection(),
                    ] else ...[
                      _buildContaEfiSection(),
                    ],
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
          // Botão fixo no rodapé
          _buildBotaoSalvar(),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xff425799)),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        inputFormatters: inputFormatters,
        validator: validator,
        maxLines: maxLines,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: Icon(
            icon,
            color: const Color(0xff425799),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.grey[50],
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          labelStyle: TextStyle(
            color: Colors.grey[700],
          ),
          hintStyle: TextStyle(
            color: Colors.grey[500],
          ),
        ),
      ),
    );
  }

  Widget _buildTipoPagamentoCard({
    required TipoPagamento tipo,
    required String titulo,
    required String subtitulo,
    required IconData icone,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _tipoPagamentoSelecionado = tipo;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xff425799).withOpacity(0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? const Color(0xff425799) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              icone,
              color: isSelected ? const Color(0xff425799) : Colors.grey[600],
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              titulo,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isSelected ? const Color(0xff425799) : Colors.grey[800],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitulo,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPixSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dados PIX',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.yellow[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.yellow[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.info, color: Colors.yellow[700], size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Taxa de 1.49% por saque via PIX',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.yellow[800],
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // Seleção do tipo de documento
        Text(
          'Tipo de Documento',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: RadioListTile<TipoDocumento>(
                title: const Text('CPF'),
                value: TipoDocumento.cpf,
                groupValue: _tipoDocumentoSelecionado,
                onChanged: (value) {
                  setState(() {
                    _tipoDocumentoSelecionado = value!;
                    _pixKeyController.clear();
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
            Expanded(
              child: RadioListTile<TipoDocumento>(
                title: const Text('CNPJ'),
                value: TipoDocumento.cnpj,
                groupValue: _tipoDocumentoSelecionado,
                onChanged: (value) {
                  setState(() {
                    _tipoDocumentoSelecionado = value!;
                    _pixKeyController.clear();
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        _buildTextField(
          controller: _pixKeyController,
          label: _tipoDocumentoSelecionado == TipoDocumento.cpf
              ? 'Chave PIX (CPF) *'
              : 'Chave PIX (CNPJ) *',
          hint: _tipoDocumentoSelecionado == TipoDocumento.cpf
              ? '000.000.000-00'
              : '00.000.000/0000-00',
          icon: Icons.pix,
          keyboardType: TextInputType.number,
          inputFormatters: [
            _tipoDocumentoSelecionado == TipoDocumento.cpf
                ? _cpfMask
                : _cnpjMask
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Chave PIX é obrigatória';
            }

            if (_tipoDocumentoSelecionado == TipoDocumento.cpf) {
              if (value.length < 14) {
                return 'CPF deve ter 11 dígitos';
              }
              String cleanCpf = value.replaceAll(RegExp(r'[^\d]'), '');
              if (!_isValidCPF(cleanCpf)) {
                return 'CPF inválido';
              }
            } else {
              if (value.length < 18) {
                return 'CNPJ deve ter 14 dígitos';
              }
              String cleanCnpj = value.replaceAll(RegExp(r'[^\d]'), '');
              if (!_isValidCNPJ(cleanCnpj)) {
                return 'CNPJ inválido';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildContaEfiSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dados da Conta EFI',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green[700], size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Sem taxa de saque para conta EFI',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.green[800],
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // Seleção do tipo de documento
        Text(
          'Tipo de Documento',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: RadioListTile<TipoDocumento>(
                title: const Text('CPF'),
                value: TipoDocumento.cpf,
                groupValue: _tipoDocumentoSelecionado,
                onChanged: (value) {
                  setState(() {
                    _tipoDocumentoSelecionado = value!;
                    _documentoEfiController.clear();
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
            Expanded(
              child: RadioListTile<TipoDocumento>(
                title: const Text('CNPJ'),
                value: TipoDocumento.cnpj,
                groupValue: _tipoDocumentoSelecionado,
                onChanged: (value) {
                  setState(() {
                    _tipoDocumentoSelecionado = value!;
                    _documentoEfiController.clear();
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        _buildTextField(
          controller: _documentoEfiController,
          label: _tipoDocumentoSelecionado == TipoDocumento.cpf
              ? 'CPF *'
              : 'CNPJ *',
          hint: _tipoDocumentoSelecionado == TipoDocumento.cpf
              ? '000.000.000-00'
              : '00.000.000/0000-00',
          icon: Icons.badge,
          keyboardType: TextInputType.number,
          inputFormatters: [
            _tipoDocumentoSelecionado == TipoDocumento.cpf
                ? _cpfMask
                : _cnpjMask
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return _tipoDocumentoSelecionado == TipoDocumento.cpf
                  ? 'CPF é obrigatório'
                  : 'CNPJ é obrigatório';
            }

            if (_tipoDocumentoSelecionado == TipoDocumento.cpf) {
              if (value.length < 14) {
                return 'CPF deve ter 11 dígitos';
              }
              String cleanCpf = value.replaceAll(RegExp(r'[^\d]'), '');
              if (!_isValidCPF(cleanCpf)) {
                return 'CPF inválido';
              }
            } else {
              if (value.length < 18) {
                return 'CNPJ deve ter 14 dígitos';
              }
              String cleanCnpj = value.replaceAll(RegExp(r'[^\d]'), '');
              if (!_isValidCNPJ(cleanCnpj)) {
                return 'CNPJ inválido';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        _buildTextField(
          controller: _contaEfiController,
          label: 'Número da Conta EFI *',
          hint: 'Digite o número da sua conta EFI',
          icon: Icons.account_balance,
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Número da conta é obrigatório';
            }
            if (value.length < 6) {
              return 'Número da conta deve ter pelo menos 6 dígitos';
            }
            return null;
          },
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildBotaoSalvar() {
    return Container(
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          width: double.infinity,
          height: 56,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [
                Color(0xff425799),
                Color.fromARGB(204, 66, 87, 153),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: const Color(0xff425799).withOpacity(0.3),
                offset: const Offset(0, 4),
                blurRadius: 10,
              ),
            ],
          ),
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            onPressed: _isSaving ? null : _saveBankDetails,
            child: _isSaving
                ? const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  )
                : const Text(
                    'Salvar Dados Bancários',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  // Função para validar CNPJ
  bool _isValidCNPJ(String cnpj) {
    // Remove caracteres não numéricos
    cnpj = cnpj.replaceAll(RegExp(r'[^\d]'), '');

    // Verifica se tem 14 dígitos
    if (cnpj.length != 14) return false;

    // Verifica se todos os dígitos são iguais
    if (cnpj.split('').every((char) => char == cnpj[0])) return false;

    // Validação do primeiro dígito verificador
    int sum = 0;
    List<int> weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];

    for (int i = 0; i < 12; i++) {
      sum += int.parse(cnpj[i]) * weights1[i];
    }

    int remainder = sum % 11;
    int digit1 = remainder < 2 ? 0 : 11 - remainder;

    if (digit1 != int.parse(cnpj[12])) return false;

    // Validação do segundo dígito verificador
    sum = 0;
    List<int> weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];

    for (int i = 0; i < 13; i++) {
      sum += int.parse(cnpj[i]) * weights2[i];
    }

    remainder = sum % 11;
    int digit2 = remainder < 2 ? 0 : 11 - remainder;

    return digit2 == int.parse(cnpj[13]);
  }

  // Função para validar CPF
  bool _isValidCPF(String cpf) {
    cpf = cpf.replaceAll(RegExp(r'[^\d]'), '');

    if (cpf.length != 11) return false;
    if (cpf.split('').every((char) => char == cpf[0])) return false;

    int soma = 0;
    for (int i = 0; i < 9; i++) {
      soma += int.parse(cpf[i]) * (10 - i);
    }
    int resto = soma % 11;
    int digito1 = resto < 2 ? 0 : 11 - resto;

    if (digito1 != int.parse(cpf[9])) return false;

    soma = 0;
    for (int i = 0; i < 10; i++) {
      soma += int.parse(cpf[i]) * (11 - i);
    }
    resto = soma % 11;
    int digito2 = resto < 2 ? 0 : 11 - resto;

    return digito2 == int.parse(cpf[10]);
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser =
          await FireStoreUtils.getCurrentUser(MyAppState.currentUser!.userID);
      if (currentUser != null) {
        setState(() {
          user = currentUser;
          MyAppState.currentUser = currentUser;

          final bancoDados = currentUser.userBankDetails;

          if (bancoDados.bankName == 'EFI') {
            // Carregar dados da Conta EFI
            _tipoPagamentoSelecionado = TipoPagamento.contaEfi;
            _contaEfiController.text = bancoDados.accountNumber;

            if (bancoDados.otherDetails == 'CPF' && bancoDados.cpf.isNotEmpty) {
              _tipoDocumentoSelecionado = TipoDocumento.cpf;
              _documentoEfiController.text = _cpfMask.maskText(bancoDados.cpf);
            } else if (bancoDados.otherDetails == 'CNPJ' &&
                bancoDados.cnpj.isNotEmpty) {
              _tipoDocumentoSelecionado = TipoDocumento.cnpj;
              _documentoEfiController.text =
                  _cnpjMask.maskText(bancoDados.cnpj);
            } else if (bancoDados.otherDetails == 'CNPJ' &&
                bancoDados.pixKey.isNotEmpty) {
              _tipoDocumentoSelecionado = TipoDocumento.cnpj;
              _documentoEfiController.text =
                  _cnpjMask.maskText(bancoDados.pixKey);
            }
          } else if (bancoDados.bankName == 'PIX' ||
              bancoDados.pixKey.isNotEmpty) {
            // Carregar dados PIX
            _tipoPagamentoSelecionado = TipoPagamento.pix;

            if (bancoDados.otherDetails == 'CPF' && bancoDados.cpf.isNotEmpty) {
              _tipoDocumentoSelecionado = TipoDocumento.cpf;
              _pixKeyController.text = _cpfMask.maskText(bancoDados.cpf);
            } else if (bancoDados.otherDetails == 'CNPJ' &&
                bancoDados.cnpj.isNotEmpty) {
              _tipoDocumentoSelecionado = TipoDocumento.cnpj;
              _pixKeyController.text = _cnpjMask.maskText(bancoDados.cnpj);
            } else if (bancoDados.otherDetails == 'CNPJ' &&
                bancoDados.pixKey.isNotEmpty) {
              _tipoDocumentoSelecionado = TipoDocumento.cnpj;
              _pixKeyController.text = _cnpjMask.maskText(bancoDados.pixKey);
            } else if (bancoDados.pixKey.isNotEmpty) {
              // Detectar automaticamente baseado no tamanho
              String documentoLimpo =
                  bancoDados.pixKey.replaceAll(RegExp(r'[^\d]'), '');
              if (documentoLimpo.length == 11) {
                _tipoDocumentoSelecionado = TipoDocumento.cpf;
                _pixKeyController.text = _cpfMask.maskText(documentoLimpo);
              } else if (documentoLimpo.length == 14) {
                _tipoDocumentoSelecionado = TipoDocumento.cnpj;
                _pixKeyController.text = _cnpjMask.maskText(documentoLimpo);
              } else {
                _pixKeyController.text = bancoDados.pixKey;
              }
            } else if (bancoDados.cpf.isNotEmpty) {
              _tipoDocumentoSelecionado = TipoDocumento.cpf;
              _pixKeyController.text = _cpfMask.maskText(bancoDados.cpf);
            } else if (bancoDados.cnpj.isNotEmpty) {
              _tipoDocumentoSelecionado = TipoDocumento.cnpj;
              _pixKeyController.text = _cnpjMask.maskText(bancoDados.cnpj);
            }
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao carregar dados: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveBankDetails() async {
    if (!_formKey.currentState!.validate()) return;
    if (!mounted) return;

    setState(() {
      _isSaving = true;
    });

    try {
      if (user == null) return;

      _limparDadosBancarios();
      _preencherDadosSelecionados();

      await _enviarDadosParaFirebase();
      _exibirSucessoESair();
    } catch (e) {
      _exibirErroSalvamento(e.toString());
    } finally {
      _finalizarSalvamento();
    }
  }

  void _limparDadosBancarios() {
    final dadosBancarios = user!.userBankDetails;
    dadosBancarios.bankName = '';
    dadosBancarios.branchName = '';
    dadosBancarios.holderName = '';
    dadosBancarios.accountNumber = '';
    dadosBancarios.otherDetails = '';
    dadosBancarios.pixKey = '';
    dadosBancarios.cpf = '';
    dadosBancarios.cnpj = '';
  }

  void _preencherDadosSelecionados() {
    if (_tipoPagamentoSelecionado == TipoPagamento.pix) {
      _configurarDadosPix();
      return;
    }
    _configurarDadosContaEfi();
  }

  void _configurarDadosPix() {
    final documentoLimpo =
        _pixKeyController.text.replaceAll(RegExp(r'[^\d]'), '');
    final dadosBancarios = user!.userBankDetails;

    dadosBancarios.bankName = 'PIX';
    dadosBancarios.pixKey = documentoLimpo;
    dadosBancarios.otherDetails = _obterTipoDocumento();

    if (_tipoDocumentoSelecionado == TipoDocumento.cpf) {
      dadosBancarios.cpf = documentoLimpo;
      return;
    }
    dadosBancarios.cnpj = documentoLimpo;
  }

  void _configurarDadosContaEfi() {
    final numeroContaLimpo = _contaEfiController.text.trim();
    final documentoLimpo =
        _documentoEfiController.text.replaceAll(RegExp(r'[^\d]'), '');
    final dadosBancarios = user!.userBankDetails;

    dadosBancarios.bankName = 'EFI';
    dadosBancarios.accountNumber = numeroContaLimpo;
    dadosBancarios.otherDetails = _obterTipoDocumento();

    if (_tipoDocumentoSelecionado == TipoDocumento.cpf) {
      dadosBancarios.cpf = documentoLimpo;
      return;
    }
    dadosBancarios.cnpj = documentoLimpo;
  }

  String _obterTipoDocumento() {
    return _tipoDocumentoSelecionado == TipoDocumento.cpf ? 'CPF' : 'CNPJ';
  }

  Future<void> _enviarDadosParaFirebase() async {
    const tentativasMaximas = 3;
    int tentativaAtual = 0;

    while (tentativaAtual < tentativasMaximas) {
      try {
        await FireStoreUtils.updateCurrentUser(user!);
        MyAppState.currentUser = user;
        return;
      } catch (e) {
        tentativaAtual++;
        if (tentativaAtual >= tentativasMaximas) {
          throw Exception(
              'Falha ao salvar após $tentativasMaximas tentativas: $e');
        }
        await Future.delayed(Duration(seconds: tentativaAtual));
      }
    }
  }

  void _exibirSucessoESair() {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            SizedBox(width: 8),
            Text("Dados bancários salvos com sucesso!"),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _exibirErroSalvamento(String erro) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Text("Erro ao salvar dados: $erro"),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _finalizarSalvamento() {
    if (mounted) {
      setState(() {
        _isSaving = false;
      });
    }
  }
}
