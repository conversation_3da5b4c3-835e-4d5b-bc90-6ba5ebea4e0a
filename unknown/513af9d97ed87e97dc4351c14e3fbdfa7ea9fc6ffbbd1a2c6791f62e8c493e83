import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/ui/vincular_ta_liso/utils/status_mapeador.dart';
import 'package:emartdriver/ui/vincular_ta_liso/utils/usuario_validador.dart';
import 'package:emartdriver/ui/vincular_ta_liso/vinculado_enum.dart';
import 'package:flutter/material.dart';

class StatusVinculoWidget extends StatelessWidget {
  final TaEntregueStatus? statusAtual;
  final User? usuarioAtual;
  final bool carregandoReavaliacao;
  final VoidCallback onVerificarStatus;
  final VoidCallback onSolicitarReavaliacao;

  const StatusVinculoWidget({
    super.key,
    required this.statusAtual,
    required this.usuarioAtual,
    required this.carregandoReavaliacao,
    required this.onVerificarStatus,
    required this.onSolicitarReavaliacao,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const SizedBox(height: 40),
          _construirIconeStatus(),
          const SizedBox(height: 24),
          _construirTituloStatus(),
          const SizedBox(height: 16),
          _construirSubtituloStatus(),
          const SizedBox(height: 32),
          _construirCartaoInformacoes(),
          const SizedBox(height: 24),
          _construirCartaoStatus(),
          const SizedBox(height: 32),
          _construirBotoesAcao(),
        ],
      ),
    );
  }

  Widget _construirBotaoReavaliacao() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: carregandoReavaliacao ? null : onSolicitarReavaliacao,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.orange[700],
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: carregandoReavaliacao
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.refresh, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Solicitar Reavaliação',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _construirBotoesAcao() {
    final statusPermiteReavaliacao =
        StatusMapeador.statusPermiteReavaliacao(statusAtual);

    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: onVerificarStatus,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[700],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.refresh, size: 20),
                SizedBox(width: 8),
                Text(
                  'Verificar Status',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        if (statusPermiteReavaliacao) ...[
          const SizedBox(height: 16),
          _construirBotaoReavaliacao(),
        ],
      ],
    );
  }

  Widget _construirCartaoInformacoes() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.person, color: Colors.blue, size: 20),
              SizedBox(width: 8),
              Text(
                'Informações da Conta',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _construirLinhaInformacao(
              'Nome:', UsuarioValidador.obterNomeCompleto(usuarioAtual)),
          _construirLinhaInformacao(
              'Status:', statusAtual?.label ?? 'Indefinido'),
        ],
      ),
    );
  }

  Widget _construirCartaoStatus() {
    final mensagem = StatusMapeador.obterMensagemDetalhada(statusAtual);
    final cor = statusAtual?.color ?? Colors.grey;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: cor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: cor.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: cor, size: 20),
              const SizedBox(width: 8),
              Text(
                statusAtual?.label ?? 'Status',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: cor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            mensagem,
            style: TextStyle(
              fontSize: 14,
              color: cor.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirIconeStatus() {
    final icone = StatusMapeador.obterIconeParaStatus(statusAtual);
    final cor = statusAtual?.color ?? Colors.grey;

    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: cor.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        icone,
        size: 60,
        color: cor,
      ),
    );
  }

  Widget _construirLinhaInformacao(String rotulo, String valor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              rotulo,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              valor,
              style: const TextStyle(
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirSubtituloStatus() {
    final subtitulo = StatusMapeador.obterSubtituloParaStatus(statusAtual);
    return Text(
      subtitulo,
      style: const TextStyle(
        fontSize: 16,
        color: Colors.grey,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _construirTituloStatus() {
    final titulo = StatusMapeador.obterTituloParaStatus(statusAtual);
    return Text(
      titulo,
      style: const TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
      textAlign: TextAlign.center,
    );
  }
}
