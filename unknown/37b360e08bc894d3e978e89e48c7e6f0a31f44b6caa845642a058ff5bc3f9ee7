import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:easy_localization/easy_localization.dart' as easyLocal;
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/CarMakes.dart';
import 'package:emartdriver/model/CarModel.dart';
import 'package:emartdriver/model/SectionModel.dart';
import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/model/VehicleType.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:emartdriver/services/TaEntregueApiService.dart';
import 'package:emartdriver/services/helper.dart';
import 'package:emartdriver/ui/auth/AuthScreen.dart';
import 'package:emartdriver/ui/container/ContainerScreen.dart';
import 'package:emartdriver/ui/signUp/VerifyCodeScreen.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:url_launcher/url_launcher.dart';

File? _carImage;
File? _image;

String? validateCpfCnpj(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'CPF é obrigatório';
  }

  final String cleanedValue = value.replaceAll(RegExp(r'[^0-9]'), '');

  if (cleanedValue.length == 11) {
    if (!_isValidCpf(cleanedValue)) {
      return 'CPF inválido';
    }
  }

  return null;
}

bool _isValidCpf(String cpf) {
  if (RegExp(r'^(\d)\1{10}$').hasMatch(cpf)) return false;

  List<int> digits = cpf.split('').map((d) => int.parse(d)).toList();

  // Valida o primeiro dígito verificador
  int sum = 0;
  for (int i = 0; i < 9; i++) {
    sum += digits[i] * (10 - i);
  }
  int firstVerifier = (sum % 11 < 2) ? 0 : 11 - (sum % 11);
  if (digits[9] != firstVerifier) {
    return false;
  }

  // Valida o segundo dígito verificador
  sum = 0;
  for (int i = 0; i < 10; i++) {
    sum += digits[i] * (11 - i);
  }
  int secondVerifier = (sum % 11 < 2) ? 0 : 11 - (sum % 11);
  if (digits[10] != secondVerifier) {
    return false;
  }

  return true;
}

class CpfInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Remove non-digit characters and limit to 11 digits (CPF length)
    final String text = newValue.text.replaceAll(RegExp(r'[^0-9]'), '');
    final String truncatedText =
        text.length > 11 ? text.substring(0, 11) : text;

    // Format as CPF: ###.###.###-##
    String newText = '';
    for (int i = 0; i < truncatedText.length; i++) {
      if (i == 3 || i == 6) {
        newText += '.';
      } else if (i == 9) {
        newText += '-';
      }
      newText += truncatedText[i];
    }

    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }
}

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State createState() => _SignUpState();
}

class _SignUpState extends State<SignUpScreen> {
  final ImagePicker _imagePicker = ImagePicker();
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _cpf_ou_cnpjController = TextEditingController();
  final TextEditingController _carPlateController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey();
  bool isUserImage = true;
  AutovalidateMode _validate = AutovalidateMode.disabled;

  // New controllers and variables for the updated form
  String? _selectedCity;
  String? _selectedVehicleType;
  final List<String> _deliveryAvailability = [];
  String? _previousExperience;
  String? _storeAffiliation;
  bool? _acceptedTerms = false;

  // Options for delivery experience
  final List<String> _deliveryExperienceOptions = ['Sim', 'Não'];

  List<String> _cities = [];

  final List<String> _vehicleTypes = [
    'Bicicleta',
    'Carro',
    'Moto',
    'VUC',
    'Caminhonete'
  ];

  final List<String> _availabilityOptions = [
    'Manhã',
    'Tarde',
    'Noite',
    'Fim de semana',
    'Outro'
  ];

  final List<String> _locations = [
    'Serviço de Entrega'
  ]; // Only delivery service option
  String? _selectedServiceType;

  List<CarMakes> carMakesList = [];

  List<CarModel> carModelList = [];

  CarMakes? selectedCarMakes;
  CarModel? selectedCarModel;

  List<VehicleType> vehicleType = [];
  List<VehicleType> rentalVehicleType = [];
  VehicleType? selectedRentalVehicleType;
  VehicleType? selectedVehicleType;
  List<SectionModel>? sectionsVal = [];
  SectionModel? selectedSection;

  String? companyOrNot = "individual";
  File? _carProofPictureFile;
  File? _driverProofPictureURLFile;

  @override
  Widget build(BuildContext context) {
    if (Platform.isAndroid) {
      retrieveLostData();
    }

    return Scaffold(
      appBar: AppBar(
        elevation: 0.0,
        backgroundColor: Colors.transparent,
        iconTheme: IconThemeData(
            color: isDarkMode(context) ? Colors.white : Colors.black),
      ),
      body: SingleChildScrollView(
        child: Container(
          margin: const EdgeInsets.only(left: 16.0, right: 16, bottom: 16),
          child: Column(
            children: [
              Align(
                  alignment: Directionality.of(context) == TextDirection.ltr
                      ? Alignment.topLeft
                      : Alignment.topRight,
                  child: Text(
                    'Faça Parte do Tá Entregue',
                    style: TextStyle(
                        color: Color(COLOR_PRIMARY),
                        fontWeight: FontWeight.bold,
                        fontSize: 25.0),
                  )),
              Padding(
                padding: const EdgeInsets.only(
                    left: 8.0, top: 32, right: 8, bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Stack(
                      alignment: Alignment.bottomCenter,
                      children: <Widget>[
                        CircleAvatar(
                          radius: 65,
                          backgroundColor: Colors.grey.shade400,
                          child: ClipOval(
                            child: SizedBox(
                              width: 170,
                              height: 170,
                              child: _image == null
                                  ? Image.asset(
                                      'assets/images/placeholder.jpg',
                                      fit: BoxFit.cover,
                                    )
                                  : Image.file(
                                      _image!,
                                      fit: BoxFit.cover,
                                    ),
                            ),
                          ),
                        ),
                        Positioned(
                          left: 80,
                          right: 0,
                          child: FloatingActionButton(
                            heroTag: 'profileImage',
                            backgroundColor: const Color(COLOR_ACCENT),
                            mini: true,
                            onPressed: () => _onCameraClick(true),
                            child: Icon(
                              CupertinoIcons.camera,
                              color: isDarkMode(context)
                                  ? Colors.black
                                  : Colors.white,
                            ),
                          ),
                        )
                      ],
                    ),
                    Stack(
                      alignment: Alignment.bottomCenter,
                      children: <Widget>[
                        CircleAvatar(
                          radius: 65,
                          backgroundColor: Colors.grey.shade400,
                          child: ClipOval(
                            child: SizedBox(
                              width: 170,
                              height: 170,
                              child: _carImage == null
                                  ? Image.asset(
                                      'assets/images/car_default_image.png',
                                      fit: BoxFit.cover,
                                    )
                                  : Image.file(
                                      _carImage!,
                                      fit: BoxFit.cover,
                                    ),
                            ),
                          ),
                        ),
                        Positioned(
                          left: 80,
                          right: 0,
                          child: FloatingActionButton(
                            heroTag: 'carImage',
                            backgroundColor: const Color(COLOR_ACCENT),
                            mini: true,
                            onPressed: () => _onCameraClick(false),
                            child: Icon(
                              CupertinoIcons.camera,
                              color: isDarkMode(context)
                                  ? Colors.black
                                  : Colors.white,
                            ),
                          ),
                        )
                      ],
                    ),
                  ],
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.only(top: 16.0, right: 8.0, left: 8.0),
                child: DropdownButtonFormField(
                  hint: const Text('Serviços'),
                  // Not necessary for Option 1
                  value: _selectedServiceType,
                  onChanged: (newValue) {
                    setState(() {
                      _selectedServiceType = newValue.toString();
                    });
                  },
                  decoration: InputDecoration(
                    contentPadding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30.0),
                        borderSide: BorderSide(
                            color: Color(COLOR_PRIMARY), width: 2.0)),
                  ),
                  items: _locations.map((location) {
                    return DropdownMenuItem(
                      value: location,
                      child: Text(location),
                    );
                  }).toList(),
                ),
              ),
              Form(
                key: _formKey,
                autovalidateMode: _validate,
                child: formUI(),
              )
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _image = null;
    _carImage = null;
    super.dispose();
  }

  // Fetch cities from Firebase 'cidades_franqueados' collection
  void fetchCitiesFromFirebase() async {
    try {
      QuerySnapshot<Map<String, dynamic>> citiesQuery = await FirebaseFirestore
          .instance
          .collection('cidades_franqueados')
          .where('isActive', isEqualTo: true)
          .get();

      List<String> fetchedCities = [];

      await Future.forEach(citiesQuery.docs,
          (QueryDocumentSnapshot<Map<String, dynamic>> document) {
        try {
          // Extract the 'cidade' field from each document
          String cityName = document.data()['cidade'];
          fetchedCities.add(cityName);
        } catch (e) {
          print('Error parsing city data: $e');
        }
      });

      // Sort cities alphabetically and update the state
      fetchedCities.sort();
      setState(() {
        _cities = fetchedCities;
      });
    } catch (e) {
      print('Error fetching cities from Firebase: $e');
    }
  }

  Widget formUI() {
    return Column(
      children: <Widget>[
        // Nome
        ConstrainedBox(
          constraints: const BoxConstraints(minWidth: double.infinity),
          child: Padding(
            padding: const EdgeInsets.only(top: 16.0, right: 8.0, left: 8.0),
            child: TextFormField(
              controller: _firstNameController,
              cursorColor: Color(COLOR_PRIMARY),
              textAlignVertical: TextAlignVertical.center,
              validator: validateName,
              textInputAction: TextInputAction.next,
              decoration: InputDecoration(
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                fillColor: Colors.white,
                hintText: 'Nome',
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25.0),
                    borderSide:
                        BorderSide(color: Color(COLOR_PRIMARY), width: 2.0)),
                errorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade200),
                  borderRadius: BorderRadius.circular(25.0),
                ),
              ),
            ),
          ),
        ),
        // CPF ou CNPJ
        ConstrainedBox(
          constraints: const BoxConstraints(minWidth: double.infinity),
          child: Padding(
            padding: const EdgeInsets.only(top: 16.0, right: 8.0, left: 8.0),
            child: TextFormField(
              controller: _cpf_ou_cnpjController,
              textAlignVertical: TextAlignVertical.center,
              textInputAction: TextInputAction.next,
              cursorColor: Color(COLOR_PRIMARY),
              validator: validateCpfCnpj,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(14),
                CpfInputFormatter(),
              ],
              decoration: InputDecoration(
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                fillColor: Colors.white,
                hintText: 'CPF',
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25.0),
                    borderSide:
                        BorderSide(color: Color(COLOR_PRIMARY), width: 2.0)),
                errorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade200),
                  borderRadius: BorderRadius.circular(25.0),
                ),
              ),
            ),
          ),
        ),
        // Email
        ConstrainedBox(
          constraints: const BoxConstraints(minWidth: double.infinity),
          child: Padding(
            padding: const EdgeInsets.only(top: 16.0, right: 8.0, left: 8.0),
            child: TextFormField(
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              textAlignVertical: TextAlignVertical.center,
              textInputAction: TextInputAction.next,
              cursorColor: Color(COLOR_PRIMARY),
              validator: validateEmail,
              decoration: InputDecoration(
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                fillColor: Colors.white,
                hintText: 'Email',
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25.0),
                    borderSide:
                        BorderSide(color: Color(COLOR_PRIMARY), width: 2.0)),
                errorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade200),
                  borderRadius: BorderRadius.circular(25.0),
                ),
              ),
            ),
          ),
        ),

        // Celular
        Padding(
          padding: const EdgeInsets.only(top: 16.0, right: 8.0, left: 8.0),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                shape: BoxShape.rectangle,
                border: Border.all(color: Colors.grey.shade200)),
            child: InternationalPhoneNumberInput(
              onInputChanged: (PhoneNumber number) =>
                  _mobileController.text = number.phoneNumber.toString(),
              ignoreBlank: true,
              autoValidateMode: AutovalidateMode.onUserInteraction,
              initialValue: PhoneNumber(
                phoneNumber: '',
                isoCode: 'BR', // Código ISO para Brasil
                dialCode: '+55', // Código de discagem para Brasil
              ),
              countries: const ['BR'], // Mostrar apenas Brasil como opção
              inputDecoration: const InputDecoration(
                hintText: 'Celular',
                border: OutlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                isDense: true,
                errorBorder: OutlineInputBorder(
                  borderSide: BorderSide.none,
                ),
              ),
              inputBorder: const OutlineInputBorder(
                borderSide: BorderSide.none,
              ),
              selectorConfig: const SelectorConfig(
                  selectorType: PhoneInputSelectorType.DIALOG),
            ),
          ),
        ),
        // Cidade
        ConstrainedBox(
          constraints: const BoxConstraints(minWidth: double.infinity),
          child: Padding(
            padding: const EdgeInsets.only(top: 16.0, right: 8.0, left: 8.0),
            child: DropdownButtonFormField<String>(
              decoration: InputDecoration(
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                fillColor: Colors.white,
                hintText: 'Cidade',
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25.0),
                    borderSide:
                        BorderSide(color: Color(COLOR_PRIMARY), width: 2.0)),
                errorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade200),
                  borderRadius: BorderRadius.circular(25.0),
                ),
              ),
              validator: (value) =>
                  value == null ? 'Selecione uma cidade' : null,
              value: _selectedCity,
              onChanged: (value) {
                setState(() {
                  _selectedCity = value;
                });
              },
              items: _cities.map((String city) {
                return DropdownMenuItem<String>(
                  value: city,
                  child: Text(city),
                );
              }).toList(),
            ),
          ),
        ),

        // Tipo de veículo
        ConstrainedBox(
          constraints: const BoxConstraints(minWidth: double.infinity),
          child: Padding(
            padding: const EdgeInsets.only(top: 16.0, right: 8.0, left: 8.0),
            child: DropdownButtonFormField<String>(
              decoration: InputDecoration(
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                fillColor: Colors.white,
                hintText: 'Tipo de veículo',
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25.0),
                    borderSide:
                        BorderSide(color: Color(COLOR_PRIMARY), width: 2.0)),
                errorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade200),
                  borderRadius: BorderRadius.circular(25.0),
                ),
              ),
              validator: (value) =>
                  value == null ? 'Selecione um tipo de veículo' : null,
              value: _selectedVehicleType,
              onChanged: (value) {
                setState(() {
                  _selectedVehicleType = value;
                });
              },
              items: _vehicleTypes.map((String vehicleType) {
                return DropdownMenuItem<String>(
                  value: vehicleType,
                  child: Text(vehicleType),
                );
              }).toList(),
            ),
          ),
        ),

        // Disponibilidade para entrega
        ConstrainedBox(
          constraints: const BoxConstraints(minWidth: double.infinity),
          child: Padding(
            padding: const EdgeInsets.only(top: 16.0, right: 8.0, left: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Disponibilidade para entrega',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  width: double.infinity,
                  child: Wrap(
                    spacing: 8.0,
                    runSpacing: 8.0,
                    children: _availabilityOptions.map((option) {
                      return FilterChip(
                        label: Text(option),
                        selected: _deliveryAvailability.contains(option),
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _deliveryAvailability.add(option);
                            } else {
                              _deliveryAvailability.remove(option);
                            }
                          });
                        },
                        backgroundColor: Colors.grey.shade200,
                        selectedColor: Color(COLOR_PRIMARY).withAlpha(51),
                        checkmarkColor: Color(COLOR_PRIMARY),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
        ),

        // Já trabalhou como entregador
        ConstrainedBox(
          constraints: const BoxConstraints(minWidth: double.infinity),
          child: Padding(
            padding: const EdgeInsets.only(top: 16.0, right: 8.0, left: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Já trabalhou como entregador?',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    contentPadding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    fillColor: Colors.white,
                    hintText: 'Selecione uma opção',
                    focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25.0),
                        borderSide: BorderSide(
                            color: Color(COLOR_PRIMARY), width: 2.0)),
                    errorBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.error),
                      borderRadius: BorderRadius.circular(25.0),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.error),
                      borderRadius: BorderRadius.circular(25.0),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey.shade200),
                      borderRadius: BorderRadius.circular(25.0),
                    ),
                  ),
                  validator: (value) =>
                      value == null ? 'Selecione uma opção' : null,
                  value: _previousExperience,
                  onChanged: (value) {
                    setState(() {
                      _previousExperience = value;
                    });
                  },
                  items: _deliveryExperienceOptions.map((String option) {
                    return DropdownMenuItem<String>(
                      value: option,
                      child: Text(option),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ),

        // Vincular a uma loja parceira
        ConstrainedBox(
          constraints: const BoxConstraints(minWidth: double.infinity),
          child: Padding(
            padding: const EdgeInsets.only(top: 16.0, right: 8.0, left: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Vincular a uma loja parceira?',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    contentPadding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    fillColor: Colors.white,
                    hintText: 'Selecione uma opção',
                    focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25.0),
                        borderSide: BorderSide(
                            color: Color(COLOR_PRIMARY), width: 2.0)),
                    errorBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.error),
                      borderRadius: BorderRadius.circular(25.0),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.error),
                      borderRadius: BorderRadius.circular(25.0),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey.shade200),
                      borderRadius: BorderRadius.circular(25.0),
                    ),
                  ),
                  validator: (value) =>
                      value == null ? 'Selecione uma opção' : null,
                  value: _storeAffiliation,
                  onChanged: (value) {
                    setState(() {
                      _storeAffiliation = value;
                    });
                  },
                  items: ['Sim', 'Não'].map((String option) {
                    return DropdownMenuItem<String>(
                      value: option,
                      child: Text(option == 'Sim'
                          ? 'Vincular a uma loja parceira'
                          : 'Não vincular a uma loja'),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ),

        ConstrainedBox(
          constraints: const BoxConstraints(minWidth: double.infinity),
          child: Padding(
            padding: const EdgeInsets.only(top: 16.0, right: 8.0, left: 8.0),
            child: TextFormField(
              obscureText: true,
              textAlignVertical: TextAlignVertical.center,
              textInputAction: TextInputAction.next,
              controller: _passwordController,
              validator: validatePassword,
              style: const TextStyle(fontSize: 18.0),
              cursorColor: Color(COLOR_PRIMARY),
              decoration: InputDecoration(
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                fillColor: Colors.white,
                hintText: 'Criar senha',
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25.0),
                    borderSide:
                        BorderSide(color: Color(COLOR_PRIMARY), width: 2.0)),
                errorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade200),
                  borderRadius: BorderRadius.circular(25.0),
                ),
              ),
            ),
          ),
        ),
        ConstrainedBox(
          constraints: const BoxConstraints(minWidth: double.infinity),
          child: Padding(
            padding: const EdgeInsets.only(top: 16.0, right: 8.0, left: 8.0),
            child: TextFormField(
              textAlignVertical: TextAlignVertical.center,
              textInputAction: TextInputAction.done,
              obscureText: true,
              controller: _confirmPasswordController,
              validator: (val) =>
                  validateConfirmPassword(_passwordController.text, val),
              style: const TextStyle(fontSize: 18.0),
              cursorColor: Color(COLOR_PRIMARY),
              decoration: InputDecoration(
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                fillColor: Colors.white,
                hintText: 'Confirmar senha',
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25.0),
                    borderSide:
                        BorderSide(color: Color(COLOR_PRIMARY), width: 2.0)),
                errorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.error),
                  borderRadius: BorderRadius.circular(25.0),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey.shade200),
                  borderRadius: BorderRadius.circular(25.0),
                ),
              ),
            ),
          ),
        ),

        // Checkbox de aceite dos termos
        Padding(
          padding: const EdgeInsets.only(top: 24.0, left: 8.0, right: 8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              StatefulBuilder(
                builder: (context, setStateCheckbox) {
                  return Checkbox(
                    value: _acceptedTerms ?? false,
                    activeColor: Color(COLOR_PRIMARY),
                    onChanged: (value) {
                      setStateCheckbox(() {
                        _acceptedTerms = value ?? false;
                      });
                      setState(() {});
                    },
                  );
                },
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () async {
                    await launchUrl(
                        Uri.parse(
                            'https://taentregue.com.br/termo/entregador.html'),
                        mode: LaunchMode.externalApplication);
                  },
                  child: RichText(
                    text: const TextSpan(
                      text: 'Concordo com os ',
                      style: TextStyle(color: Colors.black),
                      children: [
                        TextSpan(
                          text: 'termos de uso',
                          style: TextStyle(
                              color: Colors.blue,
                              decoration: TextDecoration.underline),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Botão de cadastrar
        Padding(
          padding: const EdgeInsets.only(right: 8.0, left: 8.0, bottom: 20.0),
          child: ConstrainedBox(
            constraints: const BoxConstraints(minWidth: double.infinity),
            child: Padding(
              padding: const EdgeInsets.only(
                  top: 40.0, right: 8.0, left: 8.0, bottom: 20.0),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(COLOR_PRIMARY),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25.0),
                    side: BorderSide(
                      color: Color(COLOR_PRIMARY),
                    ),
                  ),
                ),
                onPressed: (_acceptedTerms ?? false) ? () => _signUp() : null,
                child: Text(
                  'Cadastrar',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode(context) ? Colors.black : Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  getCarMakes() async {
    await FireStoreUtils.getCarMakes().then((value) {
      setState(() {
        carMakesList = value;
      });
    });

    await FireStoreUtils.getRentalVehicleType().then((value) {
      setState(() {
        rentalVehicleType = value;
      });
    });

    await FireStoreUtils.getSections().then((value) {
      setState(() {
        sectionsVal = value;
      });
    });
  }

  // Novos campos para documentos
  // File? _documentFile; // RG ou CNH dependendo do tipo de veículo
  // File? _criminalRecordFile; // Antecedentes criminais
  // File? _proofOfAddressFile; // Comprovante de residência

  @override
  void initState() {
    getCarMakes();
    fetchCitiesFromFirebase();

    super.initState();
  }

  Future<void> retrieveLostData() async {
    final LostDataResponse response = await _imagePicker.retrieveLostData();
    if (response.file != null) {
      setState(() {
        if (isUserImage) {
          _image = File(response.file!.path);
        } else {
          _carImage = File(response.file!.path);
        }
      });
    }
  }

  _handleCodigoVerificacao() async {
    await showProgress(context, 'Gerando código de verificação...', false);
    final response = await TaEntregueApiService.gerarCodigoEntregador(
        _mobileController.text.trim());
    await hideProgress();

    if (response['success'] == true && response['codigo_gerado'] != null) {
      final String codigoGerado = response['codigo_gerado'];
      final String whatsappNumber =
          response['whatsapp'] ?? _mobileController.text;

      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VerifyCodeScreen(
            correctCode: codigoGerado,
            whatsappNumber: whatsappNumber,
            onVerificationSuccess: () async {
              Navigator.pop(context);
              _signUpWithEmailAndPasswordInDeliveryService();
            },
          ),
        ),
      );
    } else {
      showAlertDialog(
          context,
          'Erro',
          response['message'] ??
              'Não foi possível gerar o código de verificação.',
          true);
    }
  }

  _onCameraClick(bool isUserImage) {
    isUserImage = isUserImage;
    final action = CupertinoActionSheet(
      message: Text(
        isUserImage ? 'Add profile picture'.tr() : 'Add Car Image'.tr(),
        style: const TextStyle(fontSize: 15.0),
      ).tr(),
      actions: <Widget>[
        CupertinoActionSheetAction(
          isDefaultAction: false,
          onPressed: () async {
            Navigator.pop(context);
            XFile? image =
                await _imagePicker.pickImage(source: ImageSource.gallery);
            if (image != null) {
              setState(() {
                isUserImage
                    ? _image = File(image.path)
                    : _carImage = File(image.path);
              });
            }
          },
          child: const Text('Choose from gallery').tr(),
        ),
        CupertinoActionSheetAction(
          isDestructiveAction: false,
          onPressed: () async {
            Navigator.pop(context);
            XFile? image =
                await _imagePicker.pickImage(source: ImageSource.camera);
            if (image != null) {
              setState(() {
                isUserImage
                    ? _image = File(image.path)
                    : _carImage = File(image.path);
              });
            }
          },
          child: const Text('Take a picture').tr(),
        ),
        CupertinoActionSheetAction(
          isDestructiveAction: true,
          onPressed: () async {
            Navigator.pop(context);
            setState(() {
              isUserImage ? _image = null : _carImage = null;
            });
          },
          child: const Text('Remove picture').tr(),
        )
      ],
      cancelButton: CupertinoActionSheetAction(
        child: const Text('Cancel').tr(),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
    );
    showCupertinoModalPopup(context: context, builder: (context) => action);
  }

  _onPickupCarProofAndDriverProof(bool isDriver) {
    final action = CupertinoActionSheet(
      message: const Text(
        'Add your Vehicle image.',
        style: TextStyle(fontSize: 15.0),
      ),
      actions: <Widget>[
        CupertinoActionSheetAction(
          isDefaultAction: false,
          onPressed: () async {
            Navigator.pop(context);
            if (isDriver) {
              XFile? singleImage =
                  await ImagePicker().pickImage(source: ImageSource.gallery);
              if (singleImage != null) {
                setState(() {
                  _driverProofPictureURLFile = File(singleImage.path);
                });
              }
            } else {
              XFile? singleImage =
                  await ImagePicker().pickImage(source: ImageSource.gallery);
              if (singleImage != null) {
                setState(() {
                  _carProofPictureFile = File(singleImage.path);
                });
              }
            }
          },
          child: const Text('Choose image from gallery'),
        ),
        CupertinoActionSheetAction(
          isDestructiveAction: false,
          onPressed: () async {
            Navigator.pop(context);
            if (isDriver) {
              final XFile? singleImage =
                  await ImagePicker().pickImage(source: ImageSource.camera);
              if (singleImage != null) {
                setState(() {
                  _driverProofPictureURLFile = File(singleImage.path);
                });
              }
            } else {
              final XFile? singleImage =
                  await ImagePicker().pickImage(source: ImageSource.camera);
              if (singleImage != null) {
                setState(() {
                  _carProofPictureFile = File(singleImage.path);
                });
              }
            }
          },
          child: const Text('Take a picture'),
        ),
      ],
      cancelButton: CupertinoActionSheetAction(
        child: const Text(
          'Cancel',
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
    );
    showCupertinoModalPopup(context: context, builder: (context) => action);
  }

  /// if the fields are validated and location is enabled we create a new user
  /// and navigate to [ContainerScreen] else we show error
  _signUp() async {
    if (_formKey.currentState?.validate() ?? false) {
      _formKey.currentState!.save();
      String? cpfCnpjError = validateCpfCnpj(_cpf_ou_cnpjController.text);
      if (cpfCnpjError != null) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(
            cpfCnpjError,
            style: const TextStyle(color: Colors.white),
          ),
          duration: const Duration(seconds: 2),
          backgroundColor: Colors.red,
        ));
        return;
      }

      // Verificar se todos os campos obrigatórios estão preenchidos
      if (_mobileController.text.isEmpty) {
        const snack = SnackBar(
          content: Text(
            'Número de telefone é obrigatório',
            style: TextStyle(color: Colors.white),
          ),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
        );
        ScaffoldMessenger.of(context).showSnackBar(snack);
        return;
      }

      if (_selectedCity == null) {
        const snack = SnackBar(
          content: Text(
            'Selecione uma cidade',
            style: TextStyle(color: Colors.white),
          ),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
        );
        ScaffoldMessenger.of(context).showSnackBar(snack);
        return;
      }

      if (_selectedVehicleType == null) {
        const snack = SnackBar(
          content: Text(
            'Selecione um tipo de veículo',
            style: TextStyle(color: Colors.white),
          ),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
        );
        ScaffoldMessenger.of(context).showSnackBar(snack);
        return;
      }

      if (_deliveryAvailability.isEmpty) {
        const snack = SnackBar(
          content: Text(
            'Selecione pelo menos um período de disponibilidade',
            style: TextStyle(color: Colors.white),
          ),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
        );
        ScaffoldMessenger.of(context).showSnackBar(snack);
        return;
      }

      if (_previousExperience == null) {
        const snack = SnackBar(
          content: Text(
            'Informe se já trabalhou como entregador',
            style: TextStyle(color: Colors.white),
          ),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
        );
        ScaffoldMessenger.of(context).showSnackBar(snack);
        return;
      }

      if (_storeAffiliation == null) {
        const snack = SnackBar(
          content: Text(
            'Informe se deseja vincular a uma loja parceira',
            style: TextStyle(color: Colors.white),
          ),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
        );
        ScaffoldMessenger.of(context).showSnackBar(snack);
        return;
      }
      if (!(_acceptedTerms ?? false)) {
        const snack = SnackBar(
          content: Text(
            'Você precisa aceitar os termos de uso para continuar',
            style: TextStyle(color: Colors.white),
          ),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
        );
        ScaffoldMessenger.of(context).showSnackBar(snack);
        return;
      }

      if (_image == null) {
        const snack = SnackBar(
          content: Text(
            'A imagem de perfil é obrigatória',
            style: TextStyle(color: Colors.white),
          ),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
        );
        ScaffoldMessenger.of(context).showSnackBar(snack);
        return;
      }

      if (_carImage == null) {
        const snack = SnackBar(
          content: Text(
            'A imagem do veículo é obrigatória',
            style: TextStyle(color: Colors.white),
          ),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
        );
        ScaffoldMessenger.of(context).showSnackBar(snack);
        return;
      }

      await _handleCodigoVerificacao();
    } else {
      setState(() {
        _validate = AutovalidateMode.onUserInteraction;
      });
    }
  }

  _signUpWithEmailAndPasswordInDeliveryService() async {
    await showProgress(context, 'Criando nova conta, aguarde...', false);

    Map<String, dynamic> additionalData = {
      'city': _selectedCity,
      'vehicleType': _selectedVehicleType,
      'deliveryAvailability': _deliveryAvailability,
      'previousExperience': _previousExperience,
      'storeAffiliation': _storeAffiliation,
    };

    dynamic result = await FireStoreUtils.firebaseSignUpWithEmailAndPassword(
        _emailController.text.trim(),
        _cpf_ou_cnpjController.text.trim(),
        _passwordController.text.trim(),
        _image,
        _carImage,
        _selectedVehicleType ?? "",
        _carPlateController.text,
        _firstNameController.text,
        "",
        _mobileController.text,
        "delivery-service");

    await hideProgress();
    // Novo tratamento de erro detalhado para upload de imagem
    if (result is String && result.startsWith('ERRO_UPLOAD:')) {
      showAlertDialog(
        context,
        'Erro no upload',
        result.replaceFirst('ERRO_UPLOAD:', '').trim(),
        true,
      );
      return;
    }
    if (result != null && result is User) {
      result.vehicleType = _selectedVehicleType ?? "";

      MyAppState.currentUser = result;
      MyAppState.currentUser!.isActive = false;
      MyAppState.currentUser!.lastOnlineTimestamp = Timestamp.now();
      await FireStoreUtils.updateCurrentUser(MyAppState.currentUser!);
      await FireStoreUtils.saveDriverAdditionalInfo(
          MyAppState.currentUser!.userID, additionalData);
      await auth.FirebaseAuth.instance.signOut();
      MyAppState.currentUser = null;
      showAlertDialog(
          context,
          'Sucesso',
          "Cadastro realizado com sucesso! Aguarde a aprovação do administrador.",
          false);
      pushAndRemoveUntil(context, const AuthScreen(), false);
    } else if (result != null && result is String) {
      showAlertDialog(context, 'Falha', result, true);
    } else {
      showAlertDialog(
          context, 'Falha', "Não foi possível realizar o cadastro", true);
    }
  }
}
