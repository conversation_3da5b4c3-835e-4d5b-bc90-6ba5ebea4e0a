import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/ui/vincular_ta_liso/utils/usuario_validador.dart';
import 'package:flutter/material.dart';

class FormularioCadastroWidget extends StatelessWidget {
  final User? usuarioAtual;
  final bool carregandoCadastro;
  final VoidCallback onRealizarCadastro;

  const FormularioCadastroWidget({
    super.key,
    required this.usuarioAtual,
    required this.carregandoCadastro,
    required this.onRealizarCadastro,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          _construirIconeCadastro(),
          const SizedBox(height: 24),
          const Text(
            'Cadastre-se no Tá entregue',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          const Text(
            'Conecte-se à plataforma Tá entregue e comece a receber mais pedidos',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          _construirCartaoInformacoesCadastro(),
          const SizedBox(height: 24),
          _construirCartaoBeneficios(),
          const SizedBox(height: 32),
          _construirBotaoCadastrar(),
        ],
      ),
    );
  }

  Widget _construirBeneficio(String texto) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          const Icon(Icons.check_circle, color: Colors.green, size: 16),
          const SizedBox(width: 8),
          Text(
            texto,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirBotaoCadastrar() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: carregandoCadastro ? null : onRealizarCadastro,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue[700],
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: carregandoCadastro
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.person_add, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Cadastrar no Tá entregue',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _construirCartaoBeneficios() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.star, color: Colors.green, size: 20),
              SizedBox(width: 8),
              Text(
                'Benefícios',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _construirBeneficio('Mais pedidos disponíveis'),
          _construirBeneficio('Maior alcance de entrega'),
          _construirBeneficio('Pagamentos seguros'),
        ],
      ),
    );
  }

  Widget _construirCartaoInformacoesCadastro() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.person_outline, color: Colors.blue, size: 20),
              SizedBox(width: 8),
              Text(
                'Seus Dados',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _construirLinhaInformacao(
              'Nome:', UsuarioValidador.obterNomeCompleto(usuarioAtual)),
          _construirLinhaInformacao(
              'E-mail:', UsuarioValidador.obterEmailOuPadrao(usuarioAtual)),
          _construirLinhaInformacao('Telefone:',
              UsuarioValidador.obterTelefoneOuPadrao(usuarioAtual)),
        ],
      ),
    );
  }

  Widget _construirIconeCadastro() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.link,
        size: 60,
        color: Colors.blue,
      ),
    );
  }

  Widget _construirLinhaInformacao(String rotulo, String valor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              rotulo,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              valor,
              style: const TextStyle(
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
