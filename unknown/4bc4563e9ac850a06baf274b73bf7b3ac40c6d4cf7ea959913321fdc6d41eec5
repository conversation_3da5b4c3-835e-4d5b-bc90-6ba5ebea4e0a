import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:emartdriver/ui/vincular_ta_liso/service/vinculo_ta_entregue_api_service.dart';
import 'package:emartdriver/ui/vincular_ta_liso/utils/mensagem_utils.dart';
import 'package:emartdriver/ui/vincular_ta_liso/utils/usuario_validador.dart';
import 'package:emartdriver/ui/vincular_ta_liso/vinculado_enum.dart';
import 'package:emartdriver/ui/vincular_ta_liso/widgets/carregamento_widget.dart';
import 'package:emartdriver/ui/vincular_ta_liso/widgets/formulario_cadastro_widget.dart';
import 'package:emartdriver/ui/vincular_ta_liso/widgets/status_vinculo_widget.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/material.dart';

class VincularTaLiso extends StatefulWidget {
  const VincularTaLiso({super.key});

  @override
  State<VincularTaLiso> createState() => _VincularTaLisoState();
}

class _VincularTaLisoState extends State<VincularTaLiso> {
  final VinculoTaEntregueApiService _apiService = VinculoTaEntregueApiService();

  bool _carregandoVerificacao = true;
  bool _carregandoCadastro = false;
  bool _carregandoReavaliacao = false;
  bool _possuiVinculo = false;
  TaEntregueStatus? _statusAtual;
  User? _usuarioAtual;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: _construirCorpo(),
    );
  }

  @override
  void initState() {
    super.initState();
    _verificarVinculoInicial();
  }

  Future<void> _atualizarStatusParaAguardando() async {
    if (_usuarioAtual != null) {
      _usuarioAtual!.ta_entregue_status = TaEntregueStatus.aguardando;
      await FireStoreUtils.updateCurrentUser(_usuarioAtual!);
      await _carregarUsuarioAtual();
      setState(() {
        _statusAtual = _usuarioAtual!.ta_entregue_status;
      });
    }
  }

  Future<void> _carregarUsuarioAtual() async {
    final firebaseUser = auth.FirebaseAuth.instance.currentUser;
    if (firebaseUser != null) {
      _usuarioAtual =
          await FireStoreUtils.getCurrentUser(MyAppState.currentUser!.userID);
    }
  }

  Widget _construirCorpo() {
    if (_carregandoVerificacao) {
      return const CarregamentoWidget();
    }

    if (_possuiVinculo) {
      return StatusVinculoWidget(
        statusAtual: _statusAtual,
        usuarioAtual: _usuarioAtual,
        carregandoReavaliacao: _carregandoReavaliacao,
        onVerificarStatus: _verificarStatusAtualizado,
        onSolicitarReavaliacao: _solicitarReavaliacao,
      );
    }

    return FormularioCadastroWidget(
      usuarioAtual: _usuarioAtual,
      carregandoCadastro: _carregandoCadastro,
      onRealizarCadastro: _realizarCadastro,
    );
  }

  void _exibirMensagemErro(String mensagem) {
    MensagemUtils.exibirErro(context, mensagem);
  }

  void _exibirMensagemInfo(String mensagem) {
    MensagemUtils.exibirInfo(context, mensagem);
  }

  void _exibirMensagemSucesso(String mensagem) {
    MensagemUtils.exibirSucesso(context, mensagem);
  }

  Future<void> _realizarCadastro() async {
    setState(() {
      _carregandoCadastro = true;
    });

    try {
      final sucesso = await _apiService.cadastrarEntregador();

      if (sucesso) {
        _exibirMensagemSucesso('Cadastro realizado com sucesso!');
        await _verificarVinculoInicial();
      } else {
        _exibirMensagemErro('Falha no cadastro. Tente novamente.');
      }
    } catch (erro) {
      _exibirMensagemErro('Erro durante o cadastro: $erro');
    } finally {
      setState(() {
        _carregandoCadastro = false;
      });
    }
  }

  Future<void> _solicitarReavaliacao() async {
    setState(() {
      _carregandoReavaliacao = true;
    });

    try {
      final sucesso = await _apiService.solicitarReavaliacao();

      if (sucesso) {
        _exibirMensagemSucesso(
            'Solicitação de reavaliação enviada com sucesso!');
        await _atualizarStatusParaAguardando();
      } else {
        _exibirMensagemErro('Falha ao solicitar reavaliação. Tente novamente.');
      }
    } catch (erro) {
      _exibirMensagemErro('Erro durante a solicitação: $erro');
    } finally {
      setState(() {
        _carregandoReavaliacao = false;
      });
    }
  }

  Future<void> _verificarStatusAtualizado() async {
    await _carregarUsuarioAtual();
    setState(() {
      _statusAtual = _usuarioAtual!.ta_entregue_status;
    });

    _exibirMensagemInfo('Status atualizado');
  }

  Future<void> _verificarVinculoInicial() async {
    try {
      await _carregarUsuarioAtual();

      if (!UsuarioValidador.usuarioEhValido(_usuarioAtual)) {
        _exibirMensagemErro('Usuário não encontrado');
        return;
      }

      final possuiVinculo = await _apiService.verificarVinculo();

      setState(() {
        _possuiVinculo = possuiVinculo;
        _statusAtual = _usuarioAtual!.ta_entregue_status;
        _carregandoVerificacao = false;
      });
    } catch (erro) {
      setState(() {
        _carregandoVerificacao = false;
      });
      _exibirMensagemErro('Erro ao verificar vínculo: $erro');
    }
  }
}
