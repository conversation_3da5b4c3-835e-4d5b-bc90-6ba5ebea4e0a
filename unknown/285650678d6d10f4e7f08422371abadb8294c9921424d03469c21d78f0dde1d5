import 'package:emartdriver/ui/vincular_ta_liso/vinculado_enum.dart';
import 'package:flutter/material.dart';

class StatusMapeador {
  static IconData obterIconeParaStatus(TaEntregueStatus? status) {
    switch (status) {
      case TaEntregueStatus.aprovado:
        return Icons.check_circle;
      case TaEntregueStatus.aguardando:
        return Icons.analytics;
      case TaEntregueStatus.suspenso:
        return Icons.pause_circle;
      case TaEntregueStatus.reprovado:
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  static String obterMensagemDetalhada(TaEntregueStatus? status) {
    switch (status) {
      case TaEntregueStatus.aprovado:
        return 'Você já pode receber pedidos da Tá Entregue.';
      case TaEntregueStatus.aguardando:
        return 'Nossa equipe está analisando sua solicitação de vínculo com a plataforma Tá entregue. Este processo pode levar até 48 horas.';
      case TaEntregueStatus.suspenso:
        return 'Sua conta foi temporariamente suspensa. Entre em contato conosco para mais informações.';
      case TaEntregueStatus.reprovado:
        return 'Sua solicitação foi reprovada. Você pode solicitar uma reavaliação clicando no botão abaixo.';
      default:
        return 'Status não reconhecido. Entre em contato com o suporte.';
    }
  }

  static String obterSubtituloParaStatus(TaEntregueStatus? status) {
    switch (status) {
      case TaEntregueStatus.aprovado:
        return 'Sua conta está vinculada';
      case TaEntregueStatus.aguardando:
        return 'Sua solicitação de vínculo está sendo analisada';
      case TaEntregueStatus.suspenso:
        return 'Entre em contato com o suporte';
      case TaEntregueStatus.reprovado:
        return 'Você pode solicitar uma reavaliação';
      default:
        return 'Verifique o status da sua conta';
    }
  }

  static String obterTituloParaStatus(TaEntregueStatus? status) {
    switch (status) {
      case TaEntregueStatus.aprovado:
        return 'Parabéns!';
      case TaEntregueStatus.aguardando:
        return 'Solicitação em Análise';
      case TaEntregueStatus.suspenso:
        return 'Conta Suspensa';
      case TaEntregueStatus.reprovado:
        return 'Solicitação Reprovada';
      default:
        return 'Status Indefinido';
    }
  }

  static bool statusPermiteReavaliacao(TaEntregueStatus? status) {
    return status == TaEntregueStatus.reprovado;
  }
}
