import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/BloqueioEntregadorModel.dart';
import 'package:emartdriver/ui/auth/AuthScreen.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class TelaBloqueioEntregador extends StatelessWidget {
  final BloqueioEntregadorModel bloqueio;

  const TelaBloqueioEntregador({
    super.key,
    required this.bloqueio,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 255, 255, 255),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 24),
                _construirIconeBloqueio(),
                const SizedBox(height: 32),
                _construirTituloPrincipal(),
                const SizedBox(height: 16),
                _construirDescricaoBloqueio(),
                const SizedBox(height: 24),
                _construirMensagemPersonalizada(),
                const SizedBox(height: 10),
                _construirTextoContato(),
                const SizedBox(height: 20),
                _construirBotaoSuporte(context),
                const SizedBox(height: 12),
                _construirBotaoLogout(context),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _abrirWhatsApp() async {
    const numeroTelefone = '5591993921571';
    const mensagem =
        'Olá, preciso de ajuda com o bloqueio da minha conta no aplicativo Tá entregue.';
    final url = Uri.parse(
        'https://wa.me/$numeroTelefone?text=${Uri.encodeComponent(mensagem)}');

    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  Widget _construirBotaoLogout(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: OutlinedButton(
        onPressed: () => _realizarLogout(context),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.red,
          side: const BorderSide(color: Colors.red),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          'Sair do Aplicativo',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _construirBotaoSuporte(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: () => _abrirWhatsApp(),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF25D366),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 2,
        ),
        icon: const Icon(Icons.chat, size: 20),
        label: const Text(
          'Contatar Suporte',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _construirDescricaoBloqueio() {
    return const Text(
      'Sua conta foi temporariamente suspensa. Para mais informações, entre em contato com o suporte.',
      style: TextStyle(
        fontSize: 16,
        color: Colors.black87,
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _construirIconeBloqueio() {
    return Image.asset(
      'assets/images/taentregue.png',
      fit: BoxFit.cover,
    );
  }

  Widget _construirMensagemPersonalizada() {
    if (bloqueio.mensagem.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        border: Border.all(color: Colors.orange.shade200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Aviso:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            bloqueio.mensagem,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirTextoContato() {
    return const Text(
      'Se você acredita que isso é um erro, entre em contato com nossa equipe de suporte. \n (91) 99392-1571',
      style: TextStyle(
        fontSize: 13,
        color: Colors.black54,
        height: 1.3,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _construirTituloPrincipal() {
    return const Text(
      'Acesso Temporariamente Bloqueado',
      style: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Colors.red,
      ),
      textAlign: TextAlign.center,
    );
  }

  Future<void> _realizarLogout(BuildContext context) async {
    try {
      await auth.FirebaseAuth.instance.signOut();
      MyAppState.currentUser = null;

      if (context.mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => const AuthScreen()),
          (route) => false,
        );
      }
    } catch (erro) {}
  }
}
