import 'package:emartdriver/model/OrderModel.dart';

extension StringExtensions on String? {
  String formatarValorMonetario() {
    if (this == null || this!.isEmpty) return '0,00';
    return this!;
  }

  String formatarDistancia() {
    if (this == null || this!.isEmpty) return '0,0';

    return this!.contains('km') ? this!.replaceAll('km', '').trim() : this!;
  }

  String obterIdReduzido() {
    if (this == null || this!.isEmpty) return '';
    return this!.length > 8 ? '${this!.substring(0, 8)}...' : this!;
  }
}

extension OrderModelExtensions on OrderModel {
  String obterEnderecoFormatado() {
    final endereco = author.shippingAddress?.first;
    if (endereco == null) return 'Não informado';

    final partes = <String>[];

    if (endereco.bairro?.isNotEmpty == true) partes.add(endereco.bairro!);
    if (endereco.cidade?.isNotEmpty == true) partes.add(endereco.cidade!);

    return partes.isEmpty ? 'Não informado' : partes.join(', ');
  }

  String obterNomeCompletoCliente() {
    return '${author.firstName} ${author.lastName}';
  }
}
