import 'package:emartdriver/model/OrderModel.dart';
import 'package:flutter/material.dart';

import 'extensions/order_extensions.dart';

class CartaoPedido extends StatelessWidget {
  final OrderModel pedido;
  final VoidCallback aoSelecionarPedido;

  const CartaoPedido({
    super.key,
    required this.pedido,
    required this.aoSelecionarPedido,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: Color(0xff425799), width: 1.5),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _construir<PERSON><PERSON><PERSON><PERSON>(),
            const SizedBox(height: 12),
            _construirInformacaoCliente(),
            const SizedBox(height: 8),
            _construirInformacaoEndereco(),
            const Sized<PERSON><PERSON>(height: 8),
            _construirInformacaoDistancia(),
            const SizedBox(height: 16),
            _construirBotaoSelecionar(),
          ],
        ),
      ),
    );
  }

  Widget _construirBotaoSelecionar() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xff425799),
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
        onPressed: aoSelecionarPedido,
        child: const Text(
          'Selecionar Pedido',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _construirCabecalho() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            'Pedido #${pedido.id.obterIdReduzido()}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Color(0xff425799),
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.green, width: 1),
          ),
          child: Text(
            'R\$ ${pedido.payValueDriver.formatarValorMonetario()}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: Colors.green,
            ),
          ),
        ),
      ],
    );
  }

  Widget _construirInformacaoCliente() {
    return Row(
      children: [
        const Icon(Icons.person_outline, size: 18, color: Colors.grey),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'Cliente: ${pedido.obterNomeCompletoCliente()}',
            style: const TextStyle(fontSize: 14, color: Colors.black87),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _construirInformacaoDistancia() {
    final distancia = pedido.distance.formatarDistancia();

    return Row(
      children: [
        const Icon(Icons.route_outlined, size: 18, color: Colors.blue),
        const SizedBox(width: 8),
        Text(
          'Distância: $distancia km',
          style: const TextStyle(fontSize: 14, color: Colors.black87),
        ),
      ],
    );
  }

  Widget _construirInformacaoEndereco() {
    final endereco = pedido.obterEnderecoFormatado();

    return Row(
      children: [
        const Icon(Icons.location_on_outlined, size: 18, color: Colors.red),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'Endereço: $endereco',
            style: const TextStyle(fontSize: 14, color: Colors.black87),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
