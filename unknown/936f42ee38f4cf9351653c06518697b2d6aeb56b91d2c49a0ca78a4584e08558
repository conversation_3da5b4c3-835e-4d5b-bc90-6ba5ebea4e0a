import 'package:flutter/material.dart';

class CarregamentoWidget extends StatelessWidget {
  const CarregamentoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          <PERSON><PERSON><PERSON><PERSON>(height: 16),
          Text(
            'Verificando status da conta...',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }
}
