import 'dart:async';
import 'dart:developer';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationService {
  StreamSubscription<Position>? _positionStreamSubscription;
  LatLng? _currentPosition;

  Function(LatLng)? onPositionChanged;

  LatLng? get currentPosition => _currentPosition;

  Future<LatLng?> determinePosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        log("Location services not enabled");
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.deniedForever) {
          log("Location permission denied forever");
          return null;
        }
      }

      final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      _currentPosition = LatLng(position.latitude, position.longitude);

      log("Current position determined: ${position.latitude}, ${position.longitude}");

      return _currentPosition;
    } catch (e) {
      log("Error determining position: $e");
      return null;
    }
  }

  void startPositionUpdates() {
    _positionStreamSubscription?.cancel();

    _positionStreamSubscription = Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10,
      ),
    ).listen((Position position) {
      _currentPosition = LatLng(position.latitude, position.longitude);

      if (onPositionChanged != null) {
        onPositionChanged!(_currentPosition!);
      }
    });
  }

  void dispose() {
    _positionStreamSubscription?.cancel();
  }
}
