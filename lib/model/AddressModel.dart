import 'package:emartdriver/model/User.dart';

class AddressModel {
  String? id;
  String? rua;
  String? address;
  String? addressAs;
  String? landmark;
  String? locality;
  UserLocation? location;
  bool? isDefault;
  String? bairro;
  String? cep;
  String? cidade;
  String? complemento;
  String? estado;
  String? logradouro;
  String? numero;
  String? pais;
  String? referencia;

  AddressModel(
      {this.id,
      this.rua,
      this.address,
      this.addressAs,
      this.landmark,
      this.locality,
      this.location,
      this.isDefault,
      this.bairro,
      this.cep,
      this.cidade,
      this.complemento,
      this.estado,
      this.logradouro,
      this.numero,
      this.pais,
      this.referencia});

  AddressModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    rua = json['rua'];
    address = json['address'];
    addressAs = json['addressAs'];
    landmark = json['landmark'];
    locality = json['locality'];
    isDefault = json['isDefault'];
    bairro = json['bairro'];
    cep = json['cep'];
    cidade = json['cidade'];
    complemento = json['complemento'];
    estado = json['estado'];
    logradouro = json['logradouro'];
    numero = json['numero'];
    pais = json['pais'];
    referencia = json['referencia'];
    location = json['location'] == null
        ? null
        : UserLocation.fromJson(json['location']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['rua'] = rua;
    data['address'] = address;
    data['addressAs'] = addressAs;
    data['landmark'] = landmark;
    data['locality'] = locality;
    data['isDefault'] = isDefault;
    data['bairro'] = bairro;
    data['cep'] = cep;
    data['cidade'] = cidade;
    data['complemento'] = complemento;
    data['estado'] = estado;
    data['logradouro'] = logradouro;
    data['numero'] = numero;
    data['pais'] = pais;
    data['referencia'] = referencia;
    if (location != null) {
      data['location'] = location!.toJson();
    }
    return data;
  }

  String getFullAddress() {
    print(address);
    print(locality);
    print(landmark);
    return '${address == null || address!.isEmpty ? "" : address} $locality ${landmark == null || landmark!.isEmpty ? "" : landmark.toString()}';
  }
}
