import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/model/DeliveryChargeModel.dart';
import 'package:emartdriver/model/User.dart';
import 'package:flutter/material.dart';

// {cidade: Lisópolis, estado: Pará, isDefault: true, complemento: 1212, numero: 100, bairro: Coqueiro, logradouro: Avenida Governador <PERSON>, location: {latitude: -1.449222, longitude: -48.499719}, cep: 67120-000, pais: Brasil}
class Address_store {
  String? cidade;
  String? estado;
  bool isDefault;
  String? complemento;
  String? numero;
  String? bairro;
  String? logradouro;
  UserLocation location;
  String? cep;
  String? pais;

  Address_store({
    this.cidade = '',
    this.estado = '',
    this.isDefault = false,
    this.complemento = '',
    this.numero = '',
    this.bairro = '',
    this.logradouro = '',
    required this.location,
    this.cep = '',
    this.pais = 'Brasil',
  });

  factory Address_store.fromJson(Map<String, dynamic> parsedJson) {
    return Address_store(
      cidade: parsedJson['cidade'] ?? '',
      estado: parsedJson['estado'] ?? '',
      isDefault: parsedJson['isDefault'] ?? false,
      complemento: parsedJson['complemento'] ?? '',
      numero: parsedJson['numero'] ?? '',
      bairro: parsedJson['bairro'] ?? '',
      logradouro: parsedJson['logradouro'] ?? '',
      location: UserLocation.fromJson(parsedJson['location']),
      cep: parsedJson['cep'] ?? '',
      pais: parsedJson['pais'] ?? 'Brasil',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cidade': cidade,
      'estado': estado,
      'isDefault': isDefault,
      'complemento': complemento,
      'numero': numero,
      'bairro': bairro,
      'logradouro': logradouro,
      'location': location,
      'cep': cep,
      'pais': pais,
    };
  }
}

class SituacaoEntregadorModel {
  final String title;
  final Color color;

  SituacaoEntregadorModel({
    required this.title,
    required this.color,
  });
}

class VendorModel {
  String author;
  String authorName;
  String authorProfilePic;
  String categoryID;
  String fcmToken;
  String categoryPhoto;
  String categoryTitle = "";
  Timestamp? createdAt;
  String description;
  String phonenumber;
  dynamic filters;
  String id;
  double latitude;
  double longitude;
  String photo;
  List<dynamic> photos;
  Address_store? address_store;
  String price;
  num reviewsCount;
  num reviewsSum;
  String title;
  String opentime;
  String closetime;
  bool hidephotos;
  bool reststatus;
  GeoFireData geoFireData;
  DeliveryChargeModel? DeliveryCharge;
  bool? isVerify;
  int? aprovadoparaEntrega;
  VendorModel(
      {this.author = '',
      this.hidephotos = false,
      this.authorName = '',
      this.authorProfilePic = '',
      this.categoryID = '',
      this.categoryPhoto = '',
      this.categoryTitle = '',
      this.createdAt,
      this.filters = const {},
      this.description = '',
      this.phonenumber = '',
      this.fcmToken = '',
      this.id = '',
      this.latitude = 0.1,
      this.longitude = 0.1,
      this.photo = '',
      this.photos = const [],
      this.address_store,
      this.price = '',
      this.reviewsCount = 0,
      this.reviewsSum = 0,
      this.closetime = '',
      this.opentime = '',
      this.title = '',
      this.reststatus = false,
      this.isVerify = false,
      this.DeliveryCharge,
      this.aprovadoparaEntrega,
      geoFireData})
      : geoFireData = geoFireData ??
            GeoFireData(
              geohash: "",
              geoPoint: const GeoPoint(0.0, 0.0),
            );

  // ,this.filters = filters ?? Filters(cuisine: '');

  factory VendorModel.fromJson(Map<String, dynamic> parsedJson,
      [Map<String, dynamic> parsedJson2 = const {}]) {
    num walVal = 0;
    if (parsedJson['walletAmount'] != null) {
      if (parsedJson['walletAmount'] is int) {
        walVal = parsedJson['walletAmount'];
      } else if (parsedJson['walletAmount'] is double) {
        walVal = parsedJson['walletAmount'].toInt();
      } else if (parsedJson['walletAmount'] is String) {
        if (parsedJson['walletAmount'].isNotEmpty) {
          walVal = num.parse(parsedJson['walletAmount']);
        } else {
          walVal = 0;
        }
      }
    }
    return VendorModel(
        aprovadoparaEntrega: parsedJson2['status_aceite_entregador'],
        author: parsedJson['author'] ?? '',
        hidephotos: parsedJson['hidephotos'] ?? false,
        authorName: parsedJson['authorName'] ?? '',
        authorProfilePic: parsedJson['authorProfilePic'] ?? '',
        categoryID: parsedJson['categoryID'] ?? '',
        categoryPhoto: parsedJson['categoryPhoto'] ?? '',
        categoryTitle: parsedJson['categoryTitle'] ?? '',
        createdAt: parsedJson['createdAt'] ?? Timestamp.now(),
        DeliveryCharge: (parsedJson.containsKey('deliveryCharge') &&
                parsedJson['deliveryCharge'] != null)
            ? DeliveryChargeModel.fromJson(parsedJson['deliveryCharge'])
            : null,
        geoFireData: parsedJson.containsKey('g')
            ? GeoFireData.fromJson(parsedJson['g'])
            : GeoFireData(
                geohash: "",
                geoPoint: const GeoPoint(0.0, 0.0),
              ),
        description: parsedJson['description'] ?? '',
        phonenumber: parsedJson['phonenumber'] ?? '',
        filters:
            // parsedJson.containsKey('filters') ?
            parsedJson['filters'] ?? [],
        // : Filters(cuisine: ''),
        id: parsedJson['id'] ?? '',
        latitude: parsedJson['latitude'] ?? 0.1,
        longitude: parsedJson['longitude'] ?? 0.1,
        photo: parsedJson['photo'] ?? '',
        photos: parsedJson['photos'] ?? [],
        fcmToken: parsedJson['fcmToken'] ?? '',
        price: parsedJson['price'] ?? '',
        isVerify: parsedJson['isVerify'] ?? false,
        reviewsCount: parsedJson['reviewsCount'] ?? 0,
        reviewsSum: parsedJson['reviewsSum'] ?? 0,
        title: parsedJson['title'] ?? '',
        closetime: parsedJson['closetime'] ?? '',
        opentime: parsedJson['opentime'] ?? '',
        address_store: Address_store.fromJson(parsedJson['address_store']),
        reststatus: parsedJson['reststatus'] ?? false);
  }

  SituacaoEntregadorModel get situation {
    switch (aprovadoparaEntrega) {
      case 0:
        return SituacaoEntregadorModel(
          title: 'Recusado',
          color: Colors.deepOrange,
        );
      case 1:
        return SituacaoEntregadorModel(
          title: 'Aguardando aceite',
          color: Colors.green,
        );
      case 2:
        return SituacaoEntregadorModel(
          title: 'Aceito',
          color: const Color.fromARGB(255, 165, 113, 0),
        );
      default:
        return SituacaoEntregadorModel(
          title: 'Indefinido',
          color: Colors.grey,
        );
    }
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = {
      'author': author,
      'hidephotos': hidephotos,
      'authorName': authorName,
      'authorProfilePic': authorProfilePic,
      'categoryID': categoryID,
      'categoryPhoto': categoryPhoto,
      'categoryTitle': categoryTitle,
      'createdAt': createdAt,
      "g": geoFireData.toJson(),
      'description': description,
      'phonenumber': phonenumber,
      'filters': filters,
      'id': id,
      'latitude': latitude,
      'longitude': longitude,
      'photo': photo,
      'photos': photos,
      'address_store': address_store!.toJson(),
      'fcmToken': fcmToken,
      'price': price,
      'reviewsCount': reviewsCount,
      'reviewsSum': reviewsSum,
      'title': title,
      'opentime': opentime,
      'closetime': closetime,
      'reststatus': reststatus,
      'isVerify': isVerify
    };
    if (DeliveryCharge != null) {
      json.addAll({'deliveryCharge': DeliveryCharge!.toJson()});
    }
    return json;
  }
}
