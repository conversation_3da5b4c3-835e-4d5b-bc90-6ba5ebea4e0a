class BloqueioEntregadorModel {
  final String id;
  final bool ativo;
  final String entregadorId;
  final String mensagem;

  const BloqueioEntregadorModel({
    required this.id,
    required this.ativo,
    required this.entregadorId,
    required this.mensagem,
  });

  factory BloqueioEntregadorModel.fromMap(
      Map<String, dynamic> mapa, String documentoId) {
    return BloqueioEntregadorModel(
      id: documentoId,
      ativo: mapa['active'] ?? false,
      entregadorId: mapa['entregador_id'] ?? '',
      mensagem: mapa['message'] ?? '',
    );
  }

  @override
  int get hashCode {
    return id.hashCode ^
        ativo.hashCode ^
        entregadorId.hashCode ^
        mensagem.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BloqueioEntregadorModel &&
        other.id == id &&
        other.ativo == ativo &&
        other.entregadorId == entregadorId &&
        other.mensagem == mensagem;
  }

  BloqueioEntregadorModel copyWith({
    String? id,
    bool? ativo,
    String? entregadorId,
    String? mensagem,
  }) {
    return BloqueioEntregadorModel(
      id: id ?? this.id,
      ativo: ativo ?? this.ativo,
      entregadorId: entregadorId ?? this.entregadorId,
      mensagem: mensagem ?? this.mensagem,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'active': ativo,
      'entregador_id': entregadorId,
      'message': mensagem,
    };
  }

  @override
  String toString() {
    return 'BloqueioEntregadorModel(id: $id, ativo: $ativo, entregadorId: $entregadorId, mensagem: $mensagem)';
  }
}
