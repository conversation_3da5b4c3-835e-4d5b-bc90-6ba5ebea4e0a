import 'package:emartdriver/model/OrderModel.dart';
import 'package:flutter/material.dart';

import 'extensions/order_extensions.dart';

class SeletorMultiplosPedidos extends StatefulWidget {
  final List<OrderModel> pedidos;
  final Function(OrderModel) aoSelecionarPedido;

  const SeletorMultiplosPedidos({
    super.key,
    required this.pedidos,
    required this.aoSelecionarPedido,
  });

  @override
  State<SeletorMultiplosPedidos> createState() =>
      _SeletorMultiplosPedidosState();
}

class _CartaoPedidoSelecionavel extends StatelessWidget {
  final OrderModel pedido;
  final bool estaSelecionado;
  final VoidCallback aoTocar;

  const _CartaoPedidoSelecionavel({
    required this.pedido,
    required this.estaSelecionado,
    required this.aoTocar,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: aoTocar,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color:
                estaSelecionado ? const Color(0xff425799) : Colors.grey[300]!,
            width: estaSelecionado ? 2 : 1,
          ),
          color: estaSelecionado
              ? const Color(0xff425799).withOpacity(0.05)
              : Colors.white,
          boxShadow: [
            BoxShadow(
              color: estaSelecionado
                  ? const Color(0xff425799).withOpacity(0.15)
                  : Colors.grey.withOpacity(0.1),
              blurRadius: estaSelecionado ? 8 : 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _construirCabecalho(),
            const SizedBox(height: 12),
            _construirDetalhes(),
            if (estaSelecionado) ...[
              const SizedBox(height: 12),
              _construirIndicadorSelecao(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _construirCabecalho() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Pedido #${pedido.id.obterIdReduzido()}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: estaSelecionado
                      ? const Color(0xff425799)
                      : Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                pedido.obterNomeCompletoCliente(),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green, width: 1),
          ),
          child: Text(
            'R\$ ${pedido.payValueDriver.formatarValorMonetario()}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: Colors.green,
            ),
          ),
        ),
      ],
    );
  }

  Widget _construirDetalhes() {
    return Column(
      children: [
        _DetalhePedido(
          icone: Icons.location_on_outlined,
          cor: Colors.red,
          texto: pedido.obterEnderecoFormatado(),
        ),
        const SizedBox(height: 6),
        _DetalhePedido(
          icone: Icons.route_outlined,
          cor: Colors.blue,
          texto: '${pedido.distance.formatarDistancia()} km',
        ),
      ],
    );
  }

  Widget _construirIndicadorSelecao() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: const Color(0xff425799).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xff425799).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.check_circle_outline,
            color: Color(0xff425799),
            size: 18,
          ),
          SizedBox(width: 6),
          Text(
            'Pedido selecionado',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Color(0xff425799),
            ),
          ),
        ],
      ),
    );
  }
}

class _DetalhePedido extends StatelessWidget {
  final IconData icone;
  final Color cor;
  final String texto;

  const _DetalhePedido({
    required this.icone,
    required this.cor,
    required this.texto,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(icone, size: 16, color: cor),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            texto,
            style: const TextStyle(
              fontSize: 13,
              color: Colors.black87,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

class _SeletorMultiplosPedidosState extends State<SeletorMultiplosPedidos> {
  OrderModel? _pedidoSelecionado;
  bool _carregandoSelecao = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          _construirIndicadorArrastavel(),
          _construirCabecalho(),
          _construirContadorPedidos(),
          const SizedBox(height: 16),
          Expanded(child: _construirListaPedidos()),
          _construirRodape(),
        ],
      ),
    );
  }

  Future<void> _confirmarSelecao() async {
    if (_pedidoSelecionado == null) return;

    setState(() {
      _carregandoSelecao = true;
    });

    // Simula um pequeno delay para UX
    await Future.delayed(const Duration(milliseconds: 300));

    if (mounted) {
      Navigator.of(context).pop();
      widget.aoSelecionarPedido(_pedidoSelecionado!);
    }
  }

  Widget _construirCabecalho() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Text(
            widget.pedidos.first.vendor.title,
            style: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Color(0xff425799),
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Text(
            'Selecione um pedido para aceitar',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _construirContadorPedidos() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xff425799).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xff425799).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.inventory_2_outlined,
            color: Color(0xff425799),
            size: 24,
          ),
          const SizedBox(width: 12),
          Text(
            '${widget.pedidos.length} pedidos disponíveis',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xff425799),
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirIndicadorArrastavel() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Container(
        width: 40,
        height: 4,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(2),
        ),
      ),
    );
  }

  Widget _construirListaPedidos() {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: widget.pedidos.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final pedido = widget.pedidos[index];
        final estaSelecionado = _pedidoSelecionado?.id == pedido.id;

        return _CartaoPedidoSelecionavel(
          pedido: pedido,
          estaSelecionado: estaSelecionado,
          aoTocar: () => _selecionarPedido(pedido),
        );
      },
    );
  }

  Widget _construirRodape() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          top: BorderSide(color: Colors.grey[200]!, width: 1),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: _pedidoSelecionado != null
                    ? const Color(0xff425799)
                    : Colors.grey[400],
                foregroundColor: Colors.white,
                elevation: _pedidoSelecionado != null ? 3 : 1,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              onPressed: _pedidoSelecionado != null && !_carregandoSelecao
                  ? _confirmarSelecao
                  : null,
              child: _carregandoSelecao
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      _pedidoSelecionado != null
                          ? 'Aceitar Pedido Selecionado'
                          : 'Selecione um pedido',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: TextButton(
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onPressed:
                  _carregandoSelecao ? null : () => Navigator.of(context).pop(),
              child: Text(
                'Cancelar',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _selecionarPedido(OrderModel pedido) {
    setState(() {
      _pedidoSelecionado = _pedidoSelecionado?.id == pedido.id ? null : pedido;
    });
  }
}
