import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/services.dart';

playSound() async {
  final audioPlayer = AudioPlayer();
  final path = await rootBundle.load("assets/audio/notification.wav");
  audioPlayer.setSourceBytes(path.buffer.asUint8List());
  audioPlayer.setReleaseMode(ReleaseMode.release);

  // Configura para parar após terminar a reprodução
  audioPlayer.onPlayerComplete.listen((event) {
    audioPlayer.stop();
    audioPlayer.dispose();
  });

  await audioPlayer.play(
    BytesSource(path.buffer.asUint8List()),
    volume: 100,
    ctx: AudioContext(
      android: const AudioContextAndroid(
          contentType: AndroidContentType.music,
          isSpeakerphoneOn: true,
          stayAwake: true,
          usageType: AndroidUsageType.alarm,
          audioFocus: AndroidAudioFocus.gainTransient),
      iOS: AudioContextIOS(category: AVAudioSessionCategory.playback),
    ),
  );
}
