import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:flutter/material.dart';

class PersistentOrderBar extends StatelessWidget {
  final bool pedidoAceito;
  final OrderModel? selectedOrder;
  final double? distanceKm;

  const PersistentOrderBar({
    Key? key,
    required this.pedidoAceito,
    required this.selectedOrder,
    this.distanceKm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!pedidoAceito || selectedOrder == null) {
      return const SizedBox.shrink();
    }

    // Só mostrar para status específicos: Entrega aceita e Entrega a caminho
    if (selectedOrder!.status != OrderStatus.driverAccepted &&
        selectedOrder!.status != OrderStatus.driverOnTheWay) {
      return const SizedBox.shrink();
    }

    final double distance = distanceKm ?? 0.0;

    String statusText;
    String destinationText;
    String secondaryText;
    Color statusColor;
    bool showingClientInfo = false;

    if (selectedOrder!.status == OrderStatus.driverAccepted) {
      // Entrega aceita - Mostrar endereço da loja
      statusText = "Entrega aceita";
      destinationText = "Loja: ${selectedOrder!.vendor.title}";

      // Mostrar endereço da loja
      String lojaEndereco = "";
      if (selectedOrder!.vendor.address_store != null) {
        final enderecoLoja = selectedOrder!.vendor.address_store!;
        final List<String> partesEndereco = [];

        if (enderecoLoja.bairro != null && enderecoLoja.bairro!.isNotEmpty) {
          partesEndereco.add(enderecoLoja.bairro!);
        }
        if (enderecoLoja.logradouro != null &&
            enderecoLoja.logradouro!.isNotEmpty) {
          partesEndereco.add(enderecoLoja.logradouro!);
        }
        if (enderecoLoja.numero != null && enderecoLoja.numero!.isNotEmpty) {
          partesEndereco.add("nº ${enderecoLoja.numero!}");
        }

        if (partesEndereco.isNotEmpty) {
          lojaEndereco = partesEndereco.join(', ');
        }
      }

      secondaryText =
          lojaEndereco.isNotEmpty ? lojaEndereco : "Endereço da loja";
      statusColor = Colors.blue;
      showingClientInfo = false;
    } else {
      // Entrega a caminho - Mostrar endereço do cliente
      statusText = "Entrega a caminho";
      final clientName =
          "${selectedOrder!.author.firstName} ${selectedOrder!.author.lastName}"
              .trim();
      final clientAddress = selectedOrder!.author.shippingAddress?.firstWhere(
          (a) => a.isDefault == true,
          orElse: () => selectedOrder!.author.shippingAddress!.first);

      destinationText =
          "Cliente: ${clientName.isNotEmpty ? clientName : 'Cliente'}";

      // Mostrar endereço do cliente
      String clienteEndereco = "";
      if (clientAddress != null) {
        final List<String> partesEndereco = [];

        if (clientAddress.bairro != null && clientAddress.bairro!.isNotEmpty) {
          partesEndereco.add(clientAddress.bairro!);
        }
        if (clientAddress.logradouro != null &&
            clientAddress.logradouro!.isNotEmpty) {
          partesEndereco.add(clientAddress.logradouro!);
        }
        if (clientAddress.numero != null && clientAddress.numero!.isNotEmpty) {
          partesEndereco.add("nº ${clientAddress.numero!}");
        }

        if (partesEndereco.isNotEmpty) {
          clienteEndereco = partesEndereco.join(', ');
        }
      }

      secondaryText =
          clienteEndereco.isNotEmpty ? clienteEndereco : "Endereço do cliente";
      statusColor = Colors.orange;
      showingClientInfo = true;
    }

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            showStoreInfoDialog(
              context,
              selectedOrder!,
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        statusText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const Spacer(),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(
                      Icons.location_on,
                      size: 16,
                      color: Color(0xff425799),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        destinationText,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xff425799),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (distance > 0) ...[
                      const SizedBox(width: 8),
                      Text(
                        distance.toString(),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      showingClientInfo ? Icons.home : Icons.store,
                      size: 16,
                      color: const Color(0xff425799),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        secondaryText,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xff425799),
                        ),
                        overflow: TextOverflow.visible,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.touch_app,
                      size: 16,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      "Toque para ver detalhes",
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[400],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
