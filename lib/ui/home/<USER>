import 'package:emartdriver/model/OrderModel.dart';
import 'package:flutter/material.dart';

void showOrderBottomSheet({
  required BuildContext context,
  required OrderModel order,
  required double distanceInKm,
  required double durationInMinutes,
  required VoidCallback onAccept,
  required VoidCallback onDecline,
  required bool pedidoAceito,
}) {
  showModalBottomSheet(
    enableDrag: true,
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.white,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (BuildContext context) {
      return Container(
        height: MediaQuery.of(context).size.height * 0.45,
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  height: 20,
                ),
                GestureDetector(
                  onTap: () {},
                  child: Container(
                    width: 50,
                    height: 5,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                )
              ],
            ),
            const SizedBox(height: 30),
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _InfoItem(
                      icon: Icons.attach_money,
                      value: "R\$ ${order.payValueDriver}",
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        color: const Color(0xff425799),
                      ),
                      width: 2,
                      height: 30,
                    ),
                    _InfoItem(
                      icon: Icons.directions_car,
                      value: "${order.distance}",
                    ),
                  ],
                ),
                const Divider(thickness: 1.5, color: Color(0xff425799)),
                const SizedBox(height: 12),
                Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE3F2FD),
                    borderRadius: BorderRadius.circular(8),
                    border:
                        Border.all(color: const Color(0xff425799), width: 1),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.store,
                              color: Color(0xff425799), size: 18),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              "Loja: ${order.vendor.title} (${order.vendor.address_store?.bairro ?? 'Bairro não informado'})",
                              style: const TextStyle(
                                color: Color(0xff425799),
                                fontWeight: FontWeight.w500,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.person_pin_circle,
                              color: Color(0xff425799), size: 18),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              "Entrega:(${order.author.shippingAddress?.firstWhere((a) => a.isDefault == true, orElse: () => order.author.shippingAddress!.first).bairro ?? 'Bairro não informado'})",
                              style: const TextStyle(
                                color: Color(0xff425799),
                                fontWeight: FontWeight.w500,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (order.has_return) ...[
              Container(
                padding: const EdgeInsets.all(10),
                decoration: const BoxDecoration(
                  color: Color.fromARGB(39, 201, 212, 247),
                ),
                width: MediaQuery.of(context).size.width,
                height: 70,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset('assets/images/caixa.png',
                        height: 30, width: 30),
                    const SizedBox(width: 8),
                    const Text(
                      "Este pedido pode ser devolvido",
                      style:
                          TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              )
            ],
            const SizedBox(height: 8),
            const Expanded(child: SizedBox()),
            Padding(
              padding: const EdgeInsets.all(18.0),
              child: pedidoAceito
                  ? Container(
                      alignment: Alignment.center,
                      height: 50,
                      width: MediaQuery.of(context).size.width * 0.7,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(30),
                        color: Colors.green,
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.check_circle_outline_rounded,
                            color: Colors.white,
                          ),
                          SizedBox(width: 8),
                          Text(
                            "Corrida aceita com sucesso!",
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ))
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            onPressed: () {
                              Navigator.pop(context);
                              onDecline();
                            },
                            child: const Text(
                              "Recusar",
                              style:
                                  TextStyle(fontSize: 14, color: Colors.white),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            onPressed: () {
                              Navigator.pop(context);
                              onAccept();
                            },
                            child: const Text(
                              "Aceitar",
                              style:
                                  TextStyle(fontSize: 14, color: Colors.white),
                            ),
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      );
    },
  );
}

class _InfoItem extends StatelessWidget {
  final String value;
  final IconData icon;

  const _InfoItem({required this.value, required this.icon});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: const Color(0xff425799)),
            padding: const EdgeInsets.all(5),
            alignment: Alignment.center,
            child: Icon(icon, size: 19, color: Colors.white)),
        const SizedBox(width: 4),
        Text(value,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
      ],
    );
  }
}
