import 'dart:async';
import 'dart:developer';
import 'dart:ui' as ui;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/OrderMonitor.dart';
import 'package:emartdriver/services/helper.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>/calular_distance.dart';
import 'package:emartdriver/ui/home/<USER>/gerenciador_modais_pedidos.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>/sound_notification.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class HomeScreen extends StatefulWidget {
  final bool isOnline;

  const HomeScreen({super.key, required this.isOnline});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  late GoogleMapController _mapController;
  final Map<String, Marker> _markers = {};
  final Set<Polyline> _polylines = {};

  LatLng? _currentPosition;
  bool _isLoading = true;

  OrderModel? _selectedOrder;
  RouteInfo? _selectedRouteInfo;
  bool _pedidoAceito = false;

  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<DocumentSnapshot>? _acceptedOrderSubscription;
  StreamSubscription<Position>? _positionStreamSubscription;
  StreamSubscription<QuerySnapshot>? _devolucaoPendenteSubscription;

  BitmapDescriptor? _deliveryPersonIcon;

  final String currentUserId = FirebaseAuth.instance.currentUser?.uid ?? '';

  final OrderMonitor _orderMonitor = OrderMonitor();

  bool _showingNewOrderNotification = false;
  bool _devolucaoBottomsheetAtivo = false;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) async {
        if (didPop) return;

        final temDevolucaoPendente = await _temPedidoComDevolucaoPendente();
        if (temDevolucaoPendente) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'Você não pode sair do aplicativo até que a devolução seja confirmada pelo lojista'),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 3),
              ),
            );

            if (!_devolucaoBottomsheetAtivo) {
              _mostrarBottomsheetDevolucaoPendente();
            }
          }
        } else {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Stack(
                children: [
                  GoogleMap(
                    mapType: MapType.terrain,
                    onMapCreated: _onMapCreated,
                    myLocationEnabled: false,
                    myLocationButtonEnabled: false,
                    zoomControlsEnabled: false,
                    markers: _markers.values.toSet(),
                    polylines: _polylines,
                    initialCameraPosition: CameraPosition(
                      target: _currentPosition ?? const LatLng(0, 0),
                      zoom: 17,
                    ),
                    onTap: _onMapTap,
                  ),
                  Positioned(
                    right: 5,
                    top: 6,
                    child: Column(
                      children: [
                        FloatingActionButton(
                          heroTag: 'zoomInButton',
                          mini: false,
                          backgroundColor: Colors.white,
                          onPressed: _zoomIn,
                          child: const Icon(
                            Icons.zoom_in_outlined,
                            color: Color(0xff425799),
                          ),
                        ),
                        const SizedBox(height: 16),
                        FloatingActionButton(
                          heroTag: "zoomOutButton",
                          mini: false,
                          backgroundColor: Colors.white,
                          onPressed: _zoomOut,
                          child: const Icon(
                            Icons.zoom_out_outlined,
                            color: Color(0xff425799),
                          ),
                        ),
                        const SizedBox(height: 16),
                        FloatingActionButton(
                          heroTag: "centerLocationButton",
                          mini: false,
                          backgroundColor: Colors.white,
                          onPressed: _centerOnCurrentLocation,
                          child: const Icon(
                            Icons.my_location,
                            color: Color(0xff425799),
                          ),
                        ),
                        const SizedBox(height: 16),
                        FloatingActionButton(
                          heroTag: "reloadMapButton",
                          mini: false,
                          backgroundColor: Colors.white,
                          onPressed: _forceMapReload,
                          child: const Icon(
                            Icons.refresh,
                            color: Color(0xff425799),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: PersistentOrderBar(
                      pedidoAceito: _pedidoAceito,
                      selectedOrder: _selectedOrder,
                      distanceKm: _selectedRouteInfo != null
                          ? (_selectedRouteInfo!.distance) / 1000
                          : null,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      log("App resumed, reloading map");

      _forceMapReload();

      _verificarDevolucaoPendente();
    }
  }

  @override
  void didUpdateWidget(HomeScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.isOnline != widget.isOnline) {
      _handleOnlineStatusChange();
    }
  }

  @override
  void dispose() {
    _ordersSubscription?.cancel();
    _acceptedOrderSubscription?.cancel();
    _positionStreamSubscription?.cancel();
    _devolucaoPendenteSubscription?.cancel();
    _orderMonitor.dispose();
    WidgetsBinding.instance.removeObserver(this);

    try {
      if (mounted) {
        _mapController.dispose();
      }
    } catch (e) {
      log("Error disposing map controller: $e");
    }
    super.dispose();
  }

  void fetchOrders() async {
    if (_currentPosition == null) return;

    if (!widget.isOnline) {
      log("User is offline, not fetching orders");
      setState(() {
        if (!_pedidoAceito) {
          _clearMarkersExceptCurrentPosition();
        }
      });
      return;
    }

    try {
      final returnOrderSnapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1)
          .get();

      if (returnOrderSnapshot.docs.isNotEmpty) {
        final returnOrder =
            OrderModel.fromJson(returnOrderSnapshot.docs.first.data());

        log("Pedido de devolução encontrado no fetchOrders: ID=${returnOrder.id}");

        LatLng? storePos =
            _getLatLng(returnOrder.vendor.address_store?.location.geoPoint);

        if (_currentPosition != null && storePos != null) {
          final returnRoute =
              await getRouteCoordinates(_currentPosition!, storePos);

          setState(() {
            _selectedOrder = returnOrder;
            _pedidoAceito = true;
            _polylines.clear();
            _clearMarkersExceptCurrentPosition();

            _polylines.add(Polyline(
              polylineId: const PolylineId("rota_de_retorno"),
              points: returnRoute,
              color: Colors.red,
              width: 5,
            ));

            _markers['destino'] = Marker(
              markerId: const MarkerId('destino'),
              position: storePos,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueGreen),
              infoWindow: InfoWindow(
                  title: "Devolução para: ${returnOrder.vendor.title}",
                  snippet: ""),
              onTap: () {
                if (_pedidoAceito && _selectedOrder != null) {
                  _showReturnBottomSheet(returnOrder);
                }
              },
            );
          });

          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              _showReturnBottomSheet(returnOrder);
            }
          });

          return;
        }
      }

      final existingOrderSnapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', whereIn: [
            OrderStatus.driverAccepted.description,
            OrderStatus.driverOnTheWay.description
          ])
          .limit(1)
          .get();

      if (existingOrderSnapshot.docs.isNotEmpty) {
        final existingOrder =
            OrderModel.fromJson(existingOrderSnapshot.docs.first.data());

        bool isDeliveryInProgress =
            existingOrder.status == OrderStatus.driverOnTheWay;

        LatLng? destinationPos;
        String destinationTitle;
        String destinationSnippet;
        String routeId;

        if (isDeliveryInProgress) {
          destinationPos = _getLatLng(existingOrder.author.shippingAddress
              ?.firstWhere((a) => a.isDefault == true,
                  orElse: () => existingOrder.author.shippingAddress!.first)
              .location
              ?.geoPoint);
          destinationTitle = "Loja: ${existingOrder.vendor.title}";
          destinationSnippet =
              "Entrega para ${existingOrder.author.firstName} ${existingOrder.author.lastName}";
          routeId = "rota_ate_cliente";
        } else {
          destinationPos =
              _getLatLng(existingOrder.vendor.address_store?.location.geoPoint);
          destinationTitle = "Loja: ${existingOrder.vendor.title}";
          destinationSnippet = "Coleta de pedido";
          routeId = "rota_ate_loja";
        }

        if (_currentPosition != null && destinationPos != null) {
          final rota =
              await getRouteCoordinates(_currentPosition!, destinationPos);
          _polylines.add(Polyline(
            polylineId: PolylineId(routeId),
            points: rota,
            color: Colors.orange,
            width: 5,
          ));

          _markers['destino'] = Marker(
            markerId: const MarkerId('destino'),
            position: destinationPos,
            icon: BitmapDescriptor.defaultMarkerWithHue(isDeliveryInProgress
                ? BitmapDescriptor.hueRed
                : BitmapDescriptor.hueGreen),
            infoWindow: InfoWindow(
                title: destinationTitle, snippet: destinationSnippet),
            onTap: () {
              if (_pedidoAceito && _selectedOrder != null) {
                showStoreInfoDialog(
                  context,
                  _selectedOrder!,
                  aoColetaCancelada: fetchOrders,
                );
              }
            },
          );
        }

        setState(() {
          _selectedOrder = existingOrder;
          _pedidoAceito = true;
        });
      } else {
        final snapshot = await FirebaseFirestore.instance
            .collection(ORDERS)
            .where('status', isEqualTo: OrderStatus.driverSearching.description)
            .get();

        Map<String, List<OrderModel>> pedidosPorLoja = {};

        for (var doc in snapshot.docs) {
          final data = doc.data();
          final order = OrderModel.fromJson(data);

          LatLng? lojaPos =
              _getLatLng(order.vendor.address_store?.location.geoPoint);

          if (lojaPos != null && _currentPosition != null) {
            double distanceInKm = calculateDistance(
                _currentPosition!.latitude,
                _currentPosition!.longitude,
                lojaPos.latitude,
                lojaPos.longitude);

            if (distanceInKm <= maxStoreDistanceInKM) {
              String lojaId = order.vendor.id;

              if (!pedidosPorLoja.containsKey(lojaId)) {
                pedidosPorLoja[lojaId] = [];
              }
              pedidosPorLoja[lojaId]!.add(order);
            } else {
              log("Store for order ${doc.id} filtered out: distance ${distanceInKm.toStringAsFixed(1)} exceeds limit of $maxStoreDistanceInKM");
            }
          }
        }

        pedidosPorLoja.forEach((lojaId, pedidos) {
          if (pedidos.isNotEmpty) {
            final primeiroPedido = pedidos.first;
            LatLng? lojaPos = _getLatLng(
                primeiroPedido.vendor.address_store?.location.geoPoint);

            if (lojaPos != null) {
              String titulo = pedidos.length == 1
                  ? "Loja: ${primeiroPedido.vendor.title}"
                  : "Loja: ${primeiroPedido.vendor.title} (${pedidos.length} pedidos)";

              _markers['loja_$lojaId'] = Marker(
                markerId: MarkerId('loja_$lojaId'),
                position: lojaPos,
                icon: BitmapDescriptor.defaultMarkerWithHue(pedidos.length > 1
                    ? BitmapDescriptor.hueOrange
                    : BitmapDescriptor.hueGreen),
                infoWindow: InfoWindow(title: titulo, snippet: ""),
                onTap: () {
                  if (pedidos.length == 1) {
                    _selectOrderAndShowPanel(pedidos.first);
                  } else {
                    GerenciadorModaisPedidos.exibirSeletorMultiplosPedidos(
                      context: context,
                      pedidos: pedidos,
                      aoSelecionarPedido: _selectOrderAndShowPanel,
                    );
                  }
                },
              );
            }
          }
        });

        setState(() {});
      }
    } catch (e) {
      log("Erro ao buscar pedidos: $e");
    }
  }

  Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  @override
  void initState() {
    super.initState();

    _loadCustomMarkerIcon().then((_) {
      _determinePosition();
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      globalContext = context;

      _verificarDevolucaoPendente();

      _iniciarMonitoramentoDevolucaoPendente();
    });

    WidgetsBinding.instance.addObserver(this);
  }

  void _acceptNewOrder(OrderModel order) async {
    final temDevolucaoPendente = await _temPedidoComDevolucaoPendente();
    if (temDevolucaoPendente) {
      _mostrarBottomsheetDevolucaoPendente();
      return;
    }

    try {
      final docRef =
          FirebaseFirestore.instance.collection(ORDERS).doc(order.id);

      await docRef.update({
        "entregador_id": currentUserId,
        "horaAceite": Timestamp.now(),
        "status": OrderStatus.driverAccepted.description,
      });

      final updatedDoc = await docRef.get();
      final updatedOrder = OrderModel.fromJson(updatedDoc.data()!);

      setState(() {
        _selectedOrder = updatedOrder;
        _pedidoAceito = true;
      });

      _listenToAcceptedOrder(_selectedOrder!.id);

      Future.delayed(const Duration(seconds: 1), () {
        Future.delayed(const Duration(milliseconds: 500), () {
          showStoreInfoDialog(
            context,
            updatedOrder,
            aoColetaCancelada: fetchOrders,
          );
        });
      });
    } catch (e) {
      log("Erro ao aceitar pedido: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Erro ao aceitar o pedido.")),
      );
    }
  }

  void _centerOnCurrentLocation() {
    if (_currentPosition == null) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Aguardando localização...'),
            duration: Duration(seconds: 2),
          ),
        );
      }
      _determinePosition();
      return;
    }

    try {
      _mapController.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: _currentPosition!,
            zoom: 17.0,
            tilt: 0,
          ),
        ),
      );

      _updateCurrentPositionMarker();
    } catch (e) {
      log("Erro ao centralizar mapa: $e");
    }
  }

  void _checkForAcceptedOrder() async {
    try {
      final returnQuery = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1);

      final returnSnapshot = await returnQuery.get();

      log("Verificando pedidos de devolução: ${returnSnapshot.docs.length} encontrados");

      if (returnSnapshot.docs.isNotEmpty) {
        final orderId = returnSnapshot.docs.first.id;
        log("Pedido de devolução encontrado com ID: $orderId");
        _listenToReturnOrder(orderId);
        return;
      }

      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', whereIn: [
        OrderStatus.driverAccepted.description,
        OrderStatus.driverOnTheWay.description,
        OrderStatus.driverPending.description
      ]).limit(1);

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        final orderId = snapshot.docs.first.id;
        _listenToAcceptedOrder(orderId);
      } else {
        _listenToAvailableOrders();
      }
    } catch (e) {
      log("Error checking for accepted orders: $e");
    }
  }

  void _clearMarkersExceptCurrentPosition() {
    final currentPositionMarker = _markers['entregador'];
    _markers.clear();
    if (currentPositionMarker != null) {
      _markers['entregador'] = currentPositionMarker;
    }
  }

  Future<void> _determinePosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        log("Location services not enabled");
        setState(() => _isLoading = false);
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.deniedForever) {
          log("Location permission denied forever");
          setState(() => _isLoading = false);
          return;
        }
      }

      final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      setState(() {
        _currentPosition = LatLng(position.latitude, position.longitude);
        _isLoading = false;

        _updateCurrentPositionMarker();
      });

      log("Current position determined: ${position.latitude}, ${position.longitude}");

      _startPositionUpdates();
      _startFirebaseListeners();

      _initializeOrderMonitor();

      if (mounted && _currentPosition != null) {
        _mapController.animateCamera(
          CameraUpdate.newLatLngZoom(_currentPosition!, 15),
        );
      }
    } catch (e) {
      log("Error determining position: $e");
      setState(() => _isLoading = false);
    }
  }

  void _fecharBottomsheetDevolucaoPendente() {
    if (_devolucaoBottomsheetAtivo && mounted && Navigator.canPop(context)) {
      Navigator.of(context).pop();
      _devolucaoBottomsheetAtivo = false;

      fetchOrders();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Devolução confirmada pelo lojista! Você já pode aceitar novos pedidos.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void _forceMapReload() {
    log("Forçando recarregamento do mapa");
    if (mounted) {
      setState(() {
        _markers.clear();
        _polylines.clear();
      });

      _determinePosition();
      fetchOrders();

      if (_currentPosition != null) {
        _updateCurrentPositionMarker();
      }
    }
  }

  LatLng? _getLatLng(GeoPoint? location) {
    if (location == null) return null;
    return LatLng(location.latitude, location.longitude);
  }

  void _handleAcceptedOrderUpdate(DocumentSnapshot docSnapshot) async {
    try {
      final existingOrder =
          OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);

      bool isDeliveryInProgress =
          existingOrder.status == OrderStatus.driverOnTheWay;

      bool statusChangedToOnTheWay = _selectedOrder != null &&
          _selectedOrder!.status != OrderStatus.driverOnTheWay.description &&
          existingOrder.status == OrderStatus.driverOnTheWay.description;

      LatLng? destinationPos;
      String destinationTitle;
      String destinationSnippet;
      String routeId;

      if (isDeliveryInProgress) {
        destinationPos = _getLatLng(existingOrder.author.shippingAddress
            ?.firstWhere((a) => a.isDefault == true,
                orElse: () => existingOrder.author.shippingAddress!.first)
            .location
            ?.geoPoint);
        destinationTitle = "Entrega";
        destinationSnippet = "";
        routeId = "rota_ate_cliente";
      } else {
        destinationPos =
            _getLatLng(existingOrder.vendor.address_store?.location.geoPoint);
        destinationTitle = "Loja";
        destinationSnippet = "";
        routeId = "rota_ate_loja";
      }

      if (_currentPosition != null && destinationPos != null) {
        final rota =
            await getRouteCoordinates(_currentPosition!, destinationPos);

        setState(() {
          _selectedOrder = existingOrder;
          _pedidoAceito = true;
          _polylines.clear();
          _clearMarkersExceptCurrentPosition();

          _polylines.add(Polyline(
            polylineId: PolylineId(routeId),
            points: rota,
            color: Colors.orange,
            width: 5,
          ));

          _markers['destino'] = Marker(
            markerId: const MarkerId('destino'),
            position: destinationPos ?? const LatLng(0, 0),
            icon: BitmapDescriptor.defaultMarkerWithHue(isDeliveryInProgress
                ? BitmapDescriptor.hueRed
                : BitmapDescriptor.hueGreen),
            infoWindow: InfoWindow(
                title: destinationTitle, snippet: destinationSnippet),
            onTap: () {
              if (_pedidoAceito && _selectedOrder != null) {
                showStoreInfoDialog(
                  context,
                  _selectedOrder!,
                  aoColetaCancelada: fetchOrders,
                );
              }
            },
          );
        });

        if (statusChangedToOnTheWay) {
          log("Pedido coletado! Mostrando informações do cliente");

          final loja =
              _getLatLng(existingOrder.vendor.address_store?.location.geoPoint);
          final cliente = destinationPos;

          if (loja != null) {
            final info1 = await getRouteInfo(loja, cliente);

            setState(() {
              _selectedRouteInfo = RouteInfo(
                route: [],
                distance: info1.distance,
                duration: info1.duration,
              );
            });

            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted) {
                _showClientBottomSheet(existingOrder);
              }
            });
          }
        }
      }
    } catch (e) {
      log("Error handling accepted order update: $e");
    }
  }

  void _handleAvailableOrdersUpdate(QuerySnapshot querySnapshot) {
    try {
      if (!widget.isOnline) {
        log("User is offline, not processing order updates");
        setState(() {
          if (!_pedidoAceito) {
            _clearMarkersExceptCurrentPosition();
          }
        });
        return;
      }

      setState(() {
        if (!_pedidoAceito) {
          _clearMarkersExceptCurrentPosition();
        }
      });

      Map<String, List<OrderModel>> pedidosPorLoja = {};

      for (var doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final order = OrderModel.fromJson(data);

        LatLng? lojaPos =
            _getLatLng(order.vendor.address_store?.location.geoPoint);

        if (lojaPos != null && !_pedidoAceito && _currentPosition != null) {
          double distanceInKm = calculateDistance(_currentPosition!.latitude,
              _currentPosition!.longitude, lojaPos.latitude, lojaPos.longitude);

          if (distanceInKm <= maxStoreDistanceInKM) {
            String lojaId = order.vendor.id;

            if (!pedidosPorLoja.containsKey(lojaId)) {
              pedidosPorLoja[lojaId] = [];
            }
            pedidosPorLoja[lojaId]!.add(order);
          } else {
            log("Store for order ${doc.id} filtered out: distance ${distanceInKm.toStringAsFixed(1)} exceeds limit of $maxStoreDistanceInKM");
          }
        }
      }

      pedidosPorLoja.forEach((lojaId, pedidos) {
        if (pedidos.isNotEmpty) {
          final primeiroPedido = pedidos.first;
          LatLng? lojaPos = _getLatLng(
              primeiroPedido.vendor.address_store?.location.geoPoint);

          if (lojaPos != null) {
            String titulo = pedidos.length == 1
                ? "Loja: ${primeiroPedido.vendor.title}"
                : "Loja: ${primeiroPedido.vendor.title} (${pedidos.length} pedidos)";

            setState(() {
              _markers['loja_$lojaId'] = Marker(
                markerId: MarkerId('loja_$lojaId'),
                position: lojaPos,
                icon: BitmapDescriptor.defaultMarkerWithHue(pedidos.length > 1
                    ? BitmapDescriptor.hueOrange
                    : BitmapDescriptor.hueGreen),
                infoWindow: InfoWindow(title: titulo, snippet: ""),
                onTap: () {
                  if (pedidos.length == 1) {
                    _selectOrderAndShowPanel(pedidos.first);
                  } else {
                    GerenciadorModaisPedidos.exibirSeletorMultiplosPedidos(
                      context: context,
                      pedidos: pedidos,
                      aoSelecionarPedido: _selectOrderAndShowPanel,
                    );
                  }
                },
              );
            });
          }
        }
      });
    } catch (e) {
      log("Error handling available orders update: $e");
    }
  }

  void _handleNewOrder(OrderModel order) {
    if (order.status == OrderStatus.driverAccepted.description &&
        order.entregador_id != null &&
        order.entregador_id!.isNotEmpty &&
        order.entregador_id != currentUserId) {
      log("Pedido ${order.id} já foi aceito por outro entregador: ${order.entregador_id}");

      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text("Este pedido já foi aceito por outro entregador"),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 3),
      ));

      return;
    }

    if (!_pedidoAceito && !_showingNewOrderNotification) {
      playSound();

      setState(() {
        _showingNewOrderNotification = true;
      });

      LatLng? storePos =
          _getLatLng(order.vendor.address_store?.location.geoPoint);
      double distanceInKm = 0.0;

      if (_currentPosition != null && storePos != null) {
        distanceInKm = calculateDistance(_currentPosition!.latitude,
            _currentPosition!.longitude, storePos.latitude, storePos.longitude);
      }

      showOrderBottomSheet(
        context: context,
        order: order,
        distanceInKm: distanceInKm,
        durationInMinutes: 0.0,
        onAccept: () => _acceptNewOrder(order),
        onDecline: () => setState(() {
          _showingNewOrderNotification = false;
        }),
        pedidoAceito: _pedidoAceito,
      );
    }
  }

  void _handleOnlineStatusChange() {
    log("Online status changed to: ${widget.isOnline}");

    if (widget.isOnline) {
      if (_currentPosition != null) {
        _initializeOrderMonitor();
        _startFirebaseListeners();

        _verificarDevolucaoPendente();
      }
    } else {
      _orderMonitor.dispose();
      _ordersSubscription?.cancel();
      _acceptedOrderSubscription?.cancel();

      setState(() {
        if (!_pedidoAceito) {
          _clearMarkersExceptCurrentPosition();
        }
      });
    }
  }

  void _handleReturnOrderUpdate(DocumentSnapshot docSnapshot) async {
    try {
      log("Processando pedido de devolução...");
      final returnOrder =
          OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);

      log("Pedido de devolução: ID=${returnOrder.id}, status=${returnOrder.status}, has_return=${returnOrder.has_return}");

      if (returnOrder.status != OrderStatus.delivered ||
          !returnOrder.has_return) {
        log("Pedido não está mais no status 'delivered' ou não tem has_return=true");
        _checkForAcceptedOrder();
        return;
      }

      LatLng? storePos =
          _getLatLng(returnOrder.vendor.address_store?.location.geoPoint);

      if (_currentPosition != null && storePos != null) {
        final returnRoute =
            await getRouteCoordinates(_currentPosition!, storePos);

        setState(() {
          _selectedOrder = returnOrder;
          _pedidoAceito = true;
          _polylines.clear();
          _clearMarkersExceptCurrentPosition();

          _polylines.add(Polyline(
            polylineId: const PolylineId("rota_de_retorno"),
            points: returnRoute,
            color: Colors.red,
            width: 5,
          ));

          _markers['destino'] = Marker(
            markerId: const MarkerId('destino'),
            position: storePos,
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueGreen),
            infoWindow: InfoWindow(
                title: "Devolução para: ${returnOrder.vendor.title}",
                snippet: ""),
            onTap: () {
              if (_pedidoAceito && _selectedOrder != null) {
                _showReturnBottomSheet(returnOrder);
              }
            },
          );
        });
      }
    } catch (e) {
      log("Error handling return order update: $e");
    }
  }

  void _iniciarMonitoramentoDevolucaoPendente() {
    _devolucaoPendenteSubscription?.cancel();

    _devolucaoPendenteSubscription = FirebaseFirestore.instance
        .collection(ORDERS)
        .where('entregador_id', isEqualTo: currentUserId)
        .where('has_return', isEqualTo: true)
        .snapshots()
        .listen((snapshot) {
      if (!mounted) return;

      final pedidosDevolucaoPendente = snapshot.docs.where((doc) {
        final data = doc.data();
        final status = data['status'] as String?;
        return status == OrderStatus.returned.description;
      }).toList();

      final temDevolucaoPendente = pedidosDevolucaoPendente.isNotEmpty;

      log("Monitoramento devolução: temDevolucaoPendente=$temDevolucaoPendente, total_has_return=${snapshot.docs.length}, pendentes=${pedidosDevolucaoPendente.length}");

      if (temDevolucaoPendente && !_devolucaoBottomsheetAtivo) {
        log("Exibindo bottomsheet de devolução pendente");
        _mostrarBottomsheetDevolucaoPendente();
      } else if (!temDevolucaoPendente && _devolucaoBottomsheetAtivo) {
        log("Fechando bottomsheet - devolução resolvida");
        _fecharBottomsheetDevolucaoPendente();
      }
    }, onError: (error) {
      log("Erro no monitoramento de devolução: $error");
    });
  }

  void _initializeOrderMonitor() {
    if (_currentPosition != null && widget.isOnline) {
      _orderMonitor.initialize(
        onNewOrder: _handleNewOrder,
        currentPosition: _currentPosition,
        currentUserId: currentUserId,
      );
    }
  }

  void _listenToAcceptedOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);

      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        _handleAcceptedOrderUpdate(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            _handleAcceptedOrderUpdate(docSnapshot);
          } else {
            _listenToAvailableOrders();
          }
        }, onError: (e) {
          log("Error listening to accepted order: $e");
        });
      } else {
        _listenToAvailableOrders();
      }
    } catch (e) {
      log("Error fetching initial accepted order: $e");
      _listenToAvailableOrders();
    }
  }

  void _listenToAvailableOrders() async {
    _acceptedOrderSubscription?.cancel();

    if (!widget.isOnline) {
      setState(() {
        if (!_pedidoAceito) {
          _clearMarkersExceptCurrentPosition();
        }
      });
      return;
    }

    try {
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('status', isEqualTo: OrderStatus.driverSearching.description);

      final snapshot = await query.get();

      _handleAvailableOrdersUpdate(snapshot);

      _ordersSubscription = query.snapshots().listen((querySnapshot) {
        _handleAvailableOrdersUpdate(querySnapshot);
      }, onError: (e) {
        log("Error listening to available orders: $e");
      });
    } catch (e) {
      log("Error fetching initial available orders: $e");
    }
  }

  void _listenToReturnOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);

      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        _handleReturnOrderUpdate(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            _handleReturnOrderUpdate(docSnapshot);
          } else {
            _listenToAvailableOrders();
          }
        }, onError: (e) {
          log("Error listening to return order: $e");
        });
      } else {
        _listenToAvailableOrders();
      }
    } catch (e) {
      log("Error fetching initial return order: $e");
      _listenToAvailableOrders();
    }
  }

  Future<void> _loadCustomMarkerIcon() async {
    try {
      final Uint8List markerIcon =
          await getBytesFromAsset('assets/images/motoentregador.png', 40);
      _deliveryPersonIcon = BitmapDescriptor.bytes(markerIcon);

      _deliveryPersonIcon ??=
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);

      setState(() {});
    } catch (e) {
      log("Error in _loadCustomMarkerIcon: $e");

      _deliveryPersonIcon =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    }
  }

  void _mostrarBottomsheetDevolucaoPendente() {
    if (_devolucaoBottomsheetAtivo) return;

    _devolucaoBottomsheetAtivo = true;

    showModalBottomSheet(
      enableDrag: false,
      isDismissible: false,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.50,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 60,
                height: 6,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              const SizedBox(height: 24),
              Icon(
                Icons.warning_rounded,
                size: 64,
                color: Colors.orange[600],
              ),
              const SizedBox(height: 16),
              const Text(
                'Retorno a Loja',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xff425799),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange[200]!),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.orange[700],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            "Retorno ao Ponto de Coleta",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Color(0xff425799),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      "Esta entrega possui retorno ao Estabelecimento.  Retorne ao estabelecimento para proceder a conclusão da entrega e devolver o item de retorno solicitado.",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        height: 1.4,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ],
                ),
              ),
              const Spacer(),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                            'Aguarde a confirmação do lojista para continuar'),
                        backgroundColor: Color(0xff425799),
                        duration: Duration(seconds: 3),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xff425799),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    'Aguardando Confirmação',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    ).then((_) {
      _devolucaoBottomsheetAtivo = false;
    });
  }

  void _onMapCreated(GoogleMapController controller) {
    try {
      _mapController = controller;
      if (_currentPosition != null) {
        controller.animateCamera(
          CameraUpdate.newLatLngZoom(_currentPosition!, 15),
        );

        _updateCurrentPositionMarker();
      } else {
        log("Current position is null in _onMapCreated, trying to determine position again");
        _determinePosition();
      }
      log("Map controller created successfully");
    } catch (e) {
      log("Error in _onMapCreated: $e");
    }
  }

  void _onMapTap(LatLng position) {
    if (_selectedOrder != null && !_pedidoAceito) {
      setState(() {
        _selectedOrder = null;
        _selectedRouteInfo = null;
        _polylines.clear();
        _clearMarkersExceptCurrentPosition();
        fetchOrders();
      });
    }
  }

  Future _selectOrderAndShowPanel(OrderModel order) async {
    final loja = _getLatLng(order.vendor.address_store?.location.geoPoint);
    final cliente = _getLatLng(order.author.shippingAddress
        ?.firstWhere((a) => a.isDefault == true)
        .location
        ?.geoPoint);

    if (_currentPosition == null || loja == null || cliente == null) return;

    double storeDistanceInKm = calculateDistance(_currentPosition!.latitude,
        _currentPosition!.longitude, loja.latitude, loja.longitude);

    log("Selected order from store at distance: ${storeDistanceInKm.toStringAsFixed(1)}");

    setState(() {
      _selectedOrder = order;
      _selectedRouteInfo = null;
      _polylines.clear();
      _clearMarkersExceptCurrentPosition();
      _pedidoAceito = false;
    });

    final routeInfo = await getRouteInfo(loja, cliente);

    final totalDistance = routeInfo.distance;
    final totalDuration = routeInfo.duration;

    final rotaParaLoja = await getRouteCoordinates(_currentPosition!, loja);

    setState(() {
      _selectedRouteInfo = RouteInfo(
        route: [],
        distance: totalDistance,
        duration: totalDuration,
      );

      _polylines.add(Polyline(
        polylineId: const PolylineId("rota_para_loja"),
        points: rotaParaLoja,
        color: Colors.orange,
        width: 5,
      ));

      bool isDeliveryInProgress = order.status == OrderStatus.driverOnTheWay;

      LatLng? destinationPos;
      String destinationTitle;
      String destinationSnippet;

      if (isDeliveryInProgress) {
        destinationPos = cliente;
        destinationTitle =
            "Entrega para: ${order.author.firstName} ${order.author.lastName}";
        destinationSnippet = "";
      } else {
        destinationPos = loja;
        destinationTitle = "Loja: ${order.vendor.title}";
        destinationSnippet = "";
      }

      _markers['destino'] = Marker(
        markerId: const MarkerId('destino'),
        position: destinationPos,
        icon: BitmapDescriptor.defaultMarkerWithHue(isDeliveryInProgress
            ? BitmapDescriptor.hueRed
            : BitmapDescriptor.hueGreen),
        infoWindow:
            InfoWindow(title: destinationTitle, snippet: destinationSnippet),
        onTap: () {
          if (_pedidoAceito && _selectedOrder != null) {
            showStoreInfoDialog(
              context,
              _selectedOrder!,
              aoColetaCancelada: fetchOrders,
            );
          } else {
            _showBottomSheet();
          }
        },
      );
    });

    _showBottomSheet();
  }

  void _showBottomSheet() {
    if (_selectedOrder == null || _selectedRouteInfo == null) return;

    showOrderBottomSheet(
      context: context,
      pedidoAceito: _pedidoAceito,
      order: _selectedOrder!,
      distanceInKm: (_selectedRouteInfo!.distance) / 1000,
      durationInMinutes: (_selectedRouteInfo!.duration) / 60,
      onAccept: () async {
        try {
          final docRef = FirebaseFirestore.instance
              .collection(ORDERS)
              .doc(_selectedOrder!.id);

          await docRef.update({
            "entregador_id": currentUserId,
            "horaAceite": Timestamp.now(),
            "status": OrderStatus.driverAccepted.description,
          });

          final updatedDoc = await docRef.get();
          final updatedOrder = OrderModel.fromJson(updatedDoc.data()!);

          setState(() {
            _selectedOrder = updatedOrder;
            _pedidoAceito = true;
          });

          _listenToAcceptedOrder(_selectedOrder!.id);

          Future.delayed(const Duration(seconds: 1), () {
            Future.delayed(const Duration(milliseconds: 500), () {
              showStoreInfoDialog(
                context,
                updatedOrder,
                aoColetaCancelada: fetchOrders,
              );
            });
          });
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text("Erro ao aceitar o pedido.")),
          );
        }
      },
      onDecline: () {
        setState(() {
          _selectedOrder = null;
          _selectedRouteInfo = null;
          _polylines.clear();
          _clearMarkersExceptCurrentPosition();
        });
      },
    );
  }

  void _showClientBottomSheet(OrderModel order) {
    if (_selectedRouteInfo == null) return;

    showOrderBottomSheet(
      context: context,
      pedidoAceito: true,
      order: order,
      distanceInKm: (_selectedRouteInfo!.distance) / 1000,
      durationInMinutes: (_selectedRouteInfo!.duration) / 60,
      onAccept: () {},
      onDecline: () {},
    );
  }

  void _showReturnBottomSheet(OrderModel returnOrder) {
    showModalBottomSheet(
      enableDrag: true,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.40,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 20),
                  GestureDetector(
                    onTap: () {},
                    child: Container(
                      width: 50,
                      height: 5,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  )
                ],
              ),
              const SizedBox(height: 20),
              Text(
                'Entrega com Retorno - ${returnOrder.vendor.title}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xff425799),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 15),
              Container(
                padding: const EdgeInsets.all(10),
                decoration: const BoxDecoration(
                  color: Color.fromARGB(39, 201, 212, 247),
                ),
                width: MediaQuery.of(context).size.width,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset('assets/images/caixa.png',
                        height: 30, width: 30),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        "Retorne ao estabelecimento para finalizar a entrega",
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10),
              Container(
                padding: const EdgeInsets.all(10),
                decoration: const BoxDecoration(
                  color: Color.fromARGB(39, 247, 201, 201),
                ),
                width: MediaQuery.of(context).size.width,
                child: Column(
                  children: [
                    Text(
                      "Loja: ${returnOrder.vendor.title}",
                      style: const TextStyle(
                          fontSize: 14, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      "${returnOrder.vendor.address_store?.logradouro ?? ""}, ${returnOrder.vendor.address_store?.numero ?? ""}, ${returnOrder.vendor.address_store?.bairro ?? ""}, ${returnOrder.vendor.address_store?.cidade ?? ""}",
                      style: const TextStyle(fontSize: 12),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      onPressed: () async {
                        Navigator.pop(context);
                        try {
                          final docRef = FirebaseFirestore.instance
                              .collection(ORDERS)
                              .doc(returnOrder.id);

                          await showProgress(
                              context, 'Confirmando Devolução', false);

                          await docRef.update({
                            'status': OrderStatus.returned.description,
                          });
                          setState(() {
                            _selectedOrder = null;
                            _selectedRouteInfo = null;
                            _polylines.clear();
                            _clearMarkersExceptCurrentPosition();
                          });

                          fetchOrders();

                          await Future.delayed(const Duration(seconds: 2));
                          await hideProgress();

                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                    content: Text(
                                        "Devolução confirmada com sucesso!")));

                            _checkForAcceptedOrder();
                          }
                        } catch (e) {
                          hideProgress();
                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                              content:
                                  Text("Erro ao confirmar devolução: $e")));
                        }
                      },
                      child: const Text(
                        "Confirmar Devolução",
                        style: TextStyle(fontSize: 14, color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  void _startFirebaseListeners() {
    _ordersSubscription?.cancel();
    _acceptedOrderSubscription?.cancel();

    _checkForAcceptedOrder();
  }

  void _startPositionUpdates() {
    _positionStreamSubscription?.cancel();

    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10,
    );

    _positionStreamSubscription = Geolocator.getPositionStream(
      locationSettings: locationSettings,
    ).listen((Position position) {
      if (mounted) {
        setState(() {
          _currentPosition = LatLng(position.latitude, position.longitude);
          _updateCurrentPositionMarker();
        });
      }
    });
  }

  Future<bool> _temPedidoComDevolucaoPendente() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('has_return', isEqualTo: true)
          .where('status', isEqualTo: OrderStatus.returned.description)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      log("Erro ao verificar pedidos com devolução pendente: $e");
      return false;
    }
  }

  void _updateCurrentPositionMarker() {
    if (_currentPosition == null) return;

    _markers['entregador'] = Marker(
      markerId: const MarkerId('entregador'),
      position: _currentPosition!,
      icon: _deliveryPersonIcon ?? BitmapDescriptor.defaultMarker,
      rotation: _currentPosition!.latitude,
    );
  }

  Future<void> _verificarDevolucaoPendente() async {
    if (!mounted || _devolucaoBottomsheetAtivo) return;

    final temDevolucaoPendente = await _temPedidoComDevolucaoPendente();
    if (temDevolucaoPendente) {
      _mostrarBottomsheetDevolucaoPendente();
    }
  }

  void _zoomIn() {
    try {
      _mapController.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom + 1.0;

        newZoom = newZoom > 20.0 ? 20.0 : newZoom;

        _mapController.animateCamera(
          CameraUpdate.zoomTo(newZoom),
        );
      });
    } catch (e) {
      log("Erro ao aumentar zoom: $e");
    }
  }

  void _zoomOut() {
    try {
      _mapController.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom - 1.0;

        newZoom = newZoom < 2.0 ? 2.0 : newZoom;

        _mapController.animateCamera(
          CameraUpdate.zoomTo(newZoom),
        );
      });
    } catch (e) {
      log("Erro ao diminuir zoom: $e");
    }
  }
}
