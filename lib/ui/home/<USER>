import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/AddressModel.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/helper.dart';
import 'package:emartdriver/ui/container/ContainerScreen.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class EnderecoCliente extends StatefulWidget {
  final OrderModel order;
  const EnderecoCliente({super.key, required this.order});

  @override
  State<EnderecoCliente> createState() => _EnderecoClienteState();
}

class _EnderecoClienteState extends State<EnderecoCliente>
    with SingleTickerProviderStateMixin {
  late AddressModel clientAddress;
  List<String> addressLines = [];

  late TextEditingController codeController;
  late StreamController<ErrorAnimationType> errorController;
  String currentText = "";
  bool isButtonEnabled = false;
  bool _isLoading = false;

  late AnimationController _buttonAnimController;
  late Animation<double> _buttonScaleAnimation;

  final Color primaryColor = const Color(0xff425799);
  final Color secondaryColor = Colors.deepOrangeAccent;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        shadowColor: Colors.white,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Color(0xff425799),
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
        leading: IconButton(
          color: Colors.white,
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: const Text('Entrega em andamento',
            style: TextStyle(color: Colors.white)),
        centerTitle: true,
        backgroundColor: const Color(0xff425799),
        elevation: 2,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            const SizedBox(height: 20),
            _buildSectionCard(
              title: "Informações do Cliente",
              icon: Icons.person_outline,
              color: secondaryColor,
              content: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "${widget.order.author.firstName} ${widget.order.author.lastName}",
                    style: theme.textTheme.titleLarge
                        ?.copyWith(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    addressLines.join(", "),
                    style: theme.textTheme.bodyLarge,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            _buildSectionCard(
              title: "Código de Confirmação",
              icon: Icons.lock_outline,
              color: secondaryColor,
              content: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Peça ao cliente o código de 4 dígitos para confirmar a entrega.",
                    style: theme.textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),
                  PinCodeTextField(
                    appContext: context,
                    length: 4,
                    controller: codeController,
                    animationType: AnimationType.fade,
                    errorAnimationController: errorController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    pinTheme: PinTheme(
                      shape: PinCodeFieldShape.box,
                      borderRadius: BorderRadius.circular(10),
                      fieldHeight: 60,
                      fieldWidth: 50,
                      activeColor: secondaryColor,
                      inactiveColor: Colors.grey.shade400,
                      selectedColor: secondaryColor,
                      activeFillColor: secondaryColor.withOpacity(0.1),
                      inactiveFillColor: Colors.white,
                      selectedFillColor: secondaryColor.withOpacity(0.2),
                    ),
                    animationDuration: const Duration(milliseconds: 250),
                    enableActiveFill: true,
                    cursorColor: secondaryColor,
                    onChanged: (value) {
                      final correctCode = widget.order.deliveryConfirmationCode;
                      setState(() {
                        isButtonEnabled = (value.length == 4 &&
                            value == correctCode.toString());

                        if (isButtonEnabled) {
                          HapticFeedback.lightImpact();
                        }
                      });
                    },
                    beforeTextPaste: (text) =>
                        text != null && int.tryParse(text) != null,
                  ),
                  if (isButtonEnabled)
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: Row(
                        children: [
                          Icon(Icons.check_circle,
                              color: secondaryColor, size: 18),
                          const SizedBox(width: 8),
                          Text(
                            "Código válido!",
                            style: TextStyle(
                              color: secondaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 100),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
        child: AnimatedBuilder(
            animation: _buttonScaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _buttonScaleAnimation.value,
                child: SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton.icon(
                    icon: _isLoading
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2.0,
                            ),
                          )
                        : const Icon(Icons.check_circle_outline),
                    label: Text(
                      _isLoading ? "Finalizando..." : "Confirmar Entrega",
                      style: const TextStyle(
                          fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    onPressed: (isButtonEnabled && !_isLoading)
                        ? _confirmDelivery
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: (isButtonEnabled && !_isLoading)
                          ? secondaryColor
                          : Colors.grey.shade400,
                      foregroundColor: Colors.white,
                      elevation: 5,
                      shadowColor: secondaryColor.withOpacity(0.3),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(14)),
                    ),
                  ),
                ),
              );
            }),
      ),
    );
  }

  @override
  void dispose() {
    codeController.dispose();
    errorController.close();
    _buttonAnimController.dispose();
    super.dispose();
  }

  @override
  @override
  void initState() {
    super.initState();
    _prepareClientAddress();
    codeController = TextEditingController();
    errorController = StreamController<ErrorAnimationType>.broadcast();
    isButtonEnabled = false;

    _buttonAnimController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );

    _buttonScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(
      CurvedAnimation(
        parent: _buttonAnimController,
        curve: Curves.easeInOut,
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required Color color,
    required Widget content,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: color.withOpacity(0.1),
                foregroundColor: color,
                child: Icon(icon),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style:
                    const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
            ],
          ),
          const SizedBox(height: 16),
          content,
        ],
      ),
    );
  }

  Future<void> _confirmDelivery() async {
    if (_isLoading) return;

    setState(() => _isLoading = true);
    _buttonAnimController.forward();

    try {
      final enteredCode = codeController.text.trim();
      final correctCode = widget.order.deliveryConfirmationCode.toString();

      if (enteredCode.length != 4) {
        errorController.add(ErrorAnimationType.shake);
        _showSnackBar("Por favor, digite o código de 4 dígitos.",
            isError: true);
        setState(() => _isLoading = false);
        _buttonAnimController.reverse();
        return;
      }

      if (enteredCode != correctCode) {
        errorController.add(ErrorAnimationType.shake);
        _showSnackBar("Código incorreto. Tente novamente.", isError: true);
        setState(() => _isLoading = false);
        _buttonAnimController.reverse();
        return;
      }

      await showProgress(context, 'Finalizando Entrega...', false);

      // Primeiro, atualizar no Firebase
      final docRef =
          FirebaseFirestore.instance.collection(ORDERS).doc(widget.order.id);
      await docRef.update({'status': OrderStatus.delivered.description});

      await hideProgress();
      if (!mounted) return;

      _showSnackBar(widget.order.has_return == true
          ? "Entrega finalizada! Retorne para a loja."
          : "Entrega finalizada com sucesso!");

      await Future.delayed(const Duration(milliseconds: 1500));
      if (!mounted) return;

      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(
          builder: (context) => ContainerScreen(user: MyAppState.currentUser!),
        ),
        (route) => false,
      );
    } catch (e, s) {
      log("Erro ao finalizar entrega: $e\n$s");
      if (mounted) {
        await hideProgress();
        _showSnackBar("Erro ao finalizar entrega. Tente novamente.",
            isError: true);
        _buttonAnimController.reverse();
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _prepareClientAddress() {
    final shippingAddresses = widget.order.author.shippingAddress;
    if (shippingAddresses != null && shippingAddresses.isNotEmpty) {
      clientAddress = shippingAddresses.firstWhere(
          (addr) => addr.isDefault == true,
          orElse: () => shippingAddresses.first);

      final logradouro = clientAddress.logradouro ?? "";
      final numero = clientAddress.numero ?? "";
      final bairro = clientAddress.bairro ?? "";
      final cidade = clientAddress.cidade ?? "";
      final complemento = clientAddress.complemento ?? "";
      final referencia = clientAddress.referencia ?? "";

      addressLines.clear();
      if (logradouro.isNotEmpty || numero.isNotEmpty) {
        addressLines.add("$logradouro${numero.isNotEmpty ? ", $numero" : ''}");
      }
      if (bairro.isNotEmpty) addressLines.add(bairro);
      if (cidade.isNotEmpty) addressLines.add(cidade);
      if (complemento.isNotEmpty) addressLines.add("Complemento: $complemento");
      if (referencia.isNotEmpty) addressLines.add("Referência: $referencia");
    } else {
      addressLines.add("Endereço não disponível.");
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error_outline : Icons.check_circle_outline,
              color: Colors.white,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
        backgroundColor: isError ? Colors.red.shade700 : secondaryColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(10),
        elevation: 6,
        duration: Duration(seconds: isError ? 4 : 2),
      ),
    );
  }
}
