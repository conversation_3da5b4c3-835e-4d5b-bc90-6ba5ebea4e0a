import 'package:emartdriver/model/OrderModel.dart';
import 'package:flutter/material.dart';

import 'cartao_pedido.dart';
import 'extensions/order_extensions.dart';

class MultiplosPedidosBottomsheet extends StatelessWidget {
  final List<OrderModel> pedidos;
  final Function(OrderModel) aoSelecionarPedido;

  const MultiplosPedidosBottomsheet({
    super.key,
    required this.pedidos,
    required this.aoSelecionarPedido,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.75,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          _construirCabecalho(),
          _construirTituloLoja(),
          _construirContadorPedidos(),
          const SizedBox(height: 16),
          Expanded(child: _construirListaPedidos(context)),
          _construirBotaoCancelar(context),
        ],
      ),
    );
  }

  Widget _construirBotaoCancelar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey[400],
            foregroundColor: Colors.white,
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(vertical: 14),
          ),
          onPressed: () => Navigator.of(context).pop(),
          child: const Text(
            'Cancelar',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  Widget _construirBotaoSelecionar(BuildContext context, OrderModel pedido) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xff425799),
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
        onPressed: () => _selecionarPedido(context, pedido),
        child: const Text(
          'Selecionar Pedido',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _construirCabecalho() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Container(
        width: 50,
        height: 5,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  Widget _construirCabecalhoPedido(OrderModel pedido) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            'Pedido #${pedido.id.obterIdReduzido()}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Color(0xff425799),
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.green, width: 1),
          ),
          child: Text(
            'R\$ ${pedido.payValueDriver.formatarValorMonetario()}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: Colors.green,
            ),
          ),
        ),
      ],
    );
  }

  Widget _construirCartaoPedido(BuildContext context, OrderModel pedido) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: Color(0xff425799), width: 1.5),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _construirCabecalhoPedido(pedido),
            const SizedBox(height: 12),
            _construirInformacaoCliente(pedido),
            const SizedBox(height: 8),
            _construirInformacaoEndereco(pedido),
            const SizedBox(height: 8),
            _construirInformacaoDistancia(pedido),
            const SizedBox(height: 16),
            _construirBotaoSelecionar(context, pedido),
          ],
        ),
      ),
    );
  }

  Widget _construirContadorPedidos() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: const BoxDecoration(
        color: Color.fromARGB(39, 201, 212, 247),
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/caixa.png',
            height: 28,
            width: 28,
          ),
          const SizedBox(width: 8),
          Text(
            "${pedidos.length} pedidos disponíveis",
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xff425799),
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirInformacaoCliente(OrderModel pedido) {
    return Row(
      children: [
        const Icon(Icons.person_outline, size: 18, color: Colors.grey),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'Cliente: ${pedido.obterNomeCompletoCliente()}',
            style: const TextStyle(fontSize: 14, color: Colors.black87),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _construirInformacaoDistancia(OrderModel pedido) {
    final distancia = pedido.distance.formatarDistancia();

    return Row(
      children: [
        const Icon(Icons.route_outlined, size: 18, color: Colors.blue),
        const SizedBox(width: 8),
        Text(
          'Distância: $distancia km',
          style: const TextStyle(fontSize: 14, color: Colors.black87),
        ),
      ],
    );
  }

  Widget _construirInformacaoEndereco(OrderModel pedido) {
    final endereco = pedido.obterEnderecoFormatado();

    return Row(
      children: [
        const Icon(Icons.location_on_outlined, size: 18, color: Colors.red),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'Endereço: $endereco',
            style: const TextStyle(fontSize: 14, color: Colors.black87),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _construirListaPedidos(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: pedidos.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final pedido = pedidos[index];
        return CartaoPedido(
          pedido: pedido,
          aoSelecionarPedido: () => _selecionarPedido(context, pedido),
        );
      },
    );
  }

  Widget _construirTituloLoja() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        'Loja: ${pedidos.first.vendor.title}',
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Color(0xff425799),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  void _selecionarPedido(BuildContext context, OrderModel pedido) {
    Navigator.of(context).pop();
    aoSelecionarPedido(pedido);
  }
}
