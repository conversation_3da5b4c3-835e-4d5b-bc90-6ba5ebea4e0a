import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>';
import 'package:flutter/material.dart';

import '../widgets/seletor_multiplos_pedidos.dart';

class GerenciadorModaisPedidos {
  static Future<void> exibirInformacoesLoja({
    required BuildContext context,
    required OrderModel pedido,
    VoidCallback? aoColetaCancelada,
  }) async {
    return showStoreInfoDialog(
      context,
      pedido,
      aoColetaCancelada: aoColetaCancelada,
    );
  }

  static Future<void> exibirModalConfirmacao({
    required BuildContext context,
    required String titulo,
    required String mensagem,
    required VoidCallback aoConfirmar,
    String? textoConfirmar,
    String? textoCancelar,
  }) async {
    return showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      builder: (BuildContext context) {
        return _ModalConfirmacao(
          titulo: titulo,
          mensagem: mensagem,
          aoConfirmar: aoConfirmar,
          textoConfirmar: textoConfirmar ?? 'Confirmar',
          textoCancelar: textoCancelar ?? 'Cancelar',
        );
      },
    );
  }

  static Future<void> exibirModalDevolucao({
    required BuildContext context,
    required OrderModel pedidoDevolucao,
  }) async {
    return showModalBottomSheet(
      enableDrag: true,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.40,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 20),
                  GestureDetector(
                    onTap: () {},
                    child: Container(
                      width: 50,
                      height: 5,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  )
                ],
              ),
              const SizedBox(height: 20),
              Text(
                'Entrega com Retorno - ${pedidoDevolucao.vendor.title}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xff425799),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 15),
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange[200]!),
                ),
                child: const Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.warning_amber_rounded,
                          color: Colors.orange,
                        ),
                        SizedBox(width: 10),
                        Expanded(
                          child: Text(
                            'Este pedido tem retorno!',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Você deve retornar o produto à loja após a entrega. '
                      'Esta entrega foi configurada para ter o produto devolvido ao estabelecimento.',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xff425799),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text(
                    'Entendi',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Future<void> exibirModalDevolucaoPendente({
    required BuildContext context,
  }) async {
    return showModalBottomSheet(
      enableDrag: false,
      isDismissible: false,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.50,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 60,
                height: 6,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              const SizedBox(height: 24),
              Icon(
                Icons.warning_rounded,
                size: 64,
                color: Colors.orange[600],
              ),
              const SizedBox(height: 16),
              const Text(
                'Retorno a Loja',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xff425799),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange[200]!),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.orange[700],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            "Retorno ao Ponto de Coleta",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Color(0xff425799),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      "Esta entrega possui retorno ao Estabelecimento.  Retorne ao estabelecimento para proceder a conclusão da entrega e devolver o item de retorno solicitado.",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        height: 1.4,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ],
                ),
              ),
              const Spacer(),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                            'Aguarde a confirmação do lojista para continuar'),
                        backgroundColor: Color(0xff425799),
                        duration: Duration(seconds: 3),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xff425799),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    'Aguardando Confirmação',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Future<void> exibirModalPedidoCliente({
    required BuildContext context,
    required OrderModel pedido,
    required RouteInfo rotaInfo,
  }) async {
    showOrderBottomSheet(
      context: context,
      pedidoAceito: true,
      order: pedido,
      distanceInKm: (rotaInfo.distance) / 1000,
      durationInMinutes: (rotaInfo.duration) / 60,
      onAccept: () {},
      onDecline: () {},
    );
  }

  static Future<void> exibirPedido({
    required BuildContext context,
    required OrderModel pedido,
    required double distanciaEmKm,
    required double duracaoEmMinutos,
    required VoidCallback aoAceitar,
    required VoidCallback aoRecusar,
    required bool pedidoAceito,
  }) async {
    return showOrderBottomSheet(
      context: context,
      order: pedido,
      distanceInKm: distanciaEmKm,
      durationInMinutes: duracaoEmMinutos,
      onAccept: aoAceitar,
      onDecline: aoRecusar,
      pedidoAceito: pedidoAceito,
    );
  }

  static Future<void> exibirSeletorMultiplosPedidos({
    required BuildContext context,
    required List<OrderModel> pedidos,
    required Function(OrderModel) aoSelecionarPedido,
  }) async {
    if (pedidos.isEmpty) return;

    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      useSafeArea: true,
      builder: (BuildContext context) {
        return SeletorMultiplosPedidos(
          pedidos: pedidos,
          aoSelecionarPedido: aoSelecionarPedido,
        );
      },
    );
  }
}

class _ModalConfirmacao extends StatelessWidget {
  final String titulo;
  final String mensagem;
  final VoidCallback aoConfirmar;
  final String textoConfirmar;
  final String textoCancelar;

  const _ModalConfirmacao({
    required this.titulo,
    required this.mensagem,
    required this.aoConfirmar,
    required this.textoConfirmar,
    required this.textoCancelar,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _construirCabecalho(),
          _construirConteudo(),
          _construirBotoes(context),
        ],
      ),
    );
  }

  Widget _construirBotoes(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: const BorderSide(color: Colors.grey),
                ),
              ),
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                textoCancelar,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xff425799),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                aoConfirmar();
              },
              child: Text(
                textoConfirmar,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirCabecalho() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Text(
        titulo,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Color(0xff425799),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _construirConteudo() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Text(
        mensagem,
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black87,
          height: 1.4,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
