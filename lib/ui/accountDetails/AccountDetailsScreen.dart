import 'package:easy_localization/easy_localization.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:emartdriver/services/helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class AccountDetailsScreen extends StatefulWidget {
  final User user;

  const AccountDetailsScreen({Key? key, required this.user}) : super(key: key);

  @override
  _AccountDetailsScreenState createState() {
    return _AccountDetailsScreenState();
  }
}

class _AccountDetailsScreenState extends State<AccountDetailsScreen> {
  late User user;
  final GlobalKey<FormState> _key = GlobalKey();
  final AutovalidateMode _validate = AutovalidateMode.disabled;
  String? firstName, lastName, carName, carPlate, email, mobile;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            'Account Details',
            style: TextStyle(
              color: isDarkMode(context) ? Colors.white : Colors.black,
            ),
          ).tr(),
        ),
        body: SingleChildScrollView(
          child: Form(
            key: _key,
            autovalidateMode: _validate,
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 16.0, right: 16, bottom: 8, top: 24),
                    child: const Text(
                      'Public Info',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ).tr(),
                  ),
                  Material(
                      elevation: 2,
                      color:
                          isDarkMode(context) ? Colors.black12 : Colors.white,
                      child: ListView(
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          children:
                              ListTile.divideTiles(context: context, tiles: [
                            ListTile(
                              title: Text(
                                'First Name',
                                style: TextStyle(
                                  color: isDarkMode(context)
                                      ? Colors.white
                                      : Colors.black,
                                ),
                              ).tr(),
                              trailing: ConstrainedBox(
                                constraints:
                                    const BoxConstraints(maxWidth: 100),
                                child: TextFormField(
                                  onSaved: (String? val) {
                                    firstName = val;
                                  },
                                  validator: validateName,
                                  textInputAction: TextInputAction.next,
                                  textAlign: TextAlign.end,
                                  initialValue: user.firstName,
                                  style: TextStyle(
                                      fontSize: 18,
                                      color: isDarkMode(context)
                                          ? Colors.white
                                          : Colors.black),
                                  cursorColor: const Color(COLOR_ACCENT),
                                  textCapitalization: TextCapitalization.words,
                                  keyboardType: TextInputType.text,
                                  decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: 'First Name'.tr(),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 5)),
                                ),
                              ),
                            ),
                            ListTile(
                              title: Text(
                                'Last Name',
                                style: TextStyle(
                                    color: isDarkMode(context)
                                        ? Colors.white
                                        : Colors.black),
                              ).tr(),
                              trailing: ConstrainedBox(
                                constraints:
                                    const BoxConstraints(maxWidth: 100),
                                child: TextFormField(
                                  onSaved: (String? val) {
                                    lastName = val;
                                  },
                                  validator: validateName,
                                  textInputAction: TextInputAction.next,
                                  textAlign: TextAlign.end,
                                  initialValue: user.lastName,
                                  style: TextStyle(
                                      fontSize: 18,
                                      color: isDarkMode(context)
                                          ? Colors.white
                                          : Colors.black),
                                  cursorColor: const Color(COLOR_ACCENT),
                                  textCapitalization: TextCapitalization.words,
                                  keyboardType: TextInputType.text,
                                  decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: 'Last Name'.tr(),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 5)),
                                ),
                              ),
                            ),
                            ListTile(
                              title: Text(
                                'Car Model',
                                style: TextStyle(
                                    color: isDarkMode(context)
                                        ? Colors.white
                                        : Colors.black),
                              ).tr(),
                              trailing: ConstrainedBox(
                                constraints:
                                    const BoxConstraints(maxWidth: 100),
                                child: TextFormField(
                                  onSaved: (String? val) {
                                    carName = val;
                                  },
                                  validator: validateEmptyField,
                                  textInputAction: TextInputAction.next,
                                  textAlign: TextAlign.end,
                                  initialValue: user.carName,
                                  style: TextStyle(
                                      fontSize: 18,
                                      color: isDarkMode(context)
                                          ? Colors.white
                                          : Colors.black),
                                  cursorColor: const Color(COLOR_ACCENT),
                                  textCapitalization: TextCapitalization.words,
                                  keyboardType: TextInputType.text,
                                  decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: 'Car Model'.tr(),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 5)),
                                ),
                              ),
                            ),
                            ListTile(
                              title: Text(
                                'Car Plate',
                                style: TextStyle(
                                    color: isDarkMode(context)
                                        ? Colors.white
                                        : Colors.black),
                              ).tr(),
                              trailing: ConstrainedBox(
                                constraints:
                                    const BoxConstraints(maxWidth: 100),
                                child: TextFormField(
                                  onSaved: (String? val) {
                                    carPlate = val;
                                  },
                                  validator: validateEmptyField,
                                  textInputAction: TextInputAction.next,
                                  textAlign: TextAlign.end,
                                  initialValue: user.carNumber,
                                  style: TextStyle(
                                      fontSize: 18,
                                      color: isDarkMode(context)
                                          ? Colors.white
                                          : Colors.black),
                                  cursorColor: const Color(COLOR_ACCENT),
                                  textCapitalization: TextCapitalization.words,
                                  keyboardType: TextInputType.text,
                                  decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: 'Car Plate'.tr(),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 5)),
                                ),
                              ),
                            ),
                          ]).toList())),
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 16.0, right: 16, bottom: 8, top: 24),
                    child: const Text(
                      'Private Details',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ).tr(),
                  ),
                  Material(
                    elevation: 2,
                    color: isDarkMode(context) ? Colors.black12 : Colors.white,
                    child: ListView(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        children: ListTile.divideTiles(
                          context: context,
                          tiles: [
                            ListTile(
                              title: Text(
                                'Email Address',
                                style: TextStyle(
                                    color: isDarkMode(context)
                                        ? Colors.white
                                        : Colors.black),
                              ).tr(),
                              trailing: ConstrainedBox(
                                constraints:
                                    const BoxConstraints(maxWidth: 200),
                                child: TextFormField(
                                  onSaved: (String? val) {
                                    email = val;
                                  },
                                  validator: validateEmail,
                                  textInputAction: TextInputAction.next,
                                  initialValue: user.email,
                                  textAlign: TextAlign.end,
                                  enabled: false,
                                  style: TextStyle(
                                      fontSize: 18,
                                      color: isDarkMode(context)
                                          ? Colors.white
                                          : Colors.black),
                                  cursorColor: const Color(COLOR_ACCENT),
                                  keyboardType: TextInputType.emailAddress,
                                  decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: 'Email Address'.tr(),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 5)),
                                ),
                              ),
                            ),
                            ListTile(
                              title: Text(
                                'Phone Number',
                                style: TextStyle(
                                    color: isDarkMode(context)
                                        ? Colors.white
                                        : Colors.black),
                              ).tr(),
                              trailing: ConstrainedBox(
                                constraints:
                                    const BoxConstraints(maxWidth: 150),
                                child: TextFormField(
                                  onSaved: (String? val) {
                                    mobile = val;
                                  },
                                  textInputAction: TextInputAction.done,
                                  initialValue: user.phoneNumber,
                                  textAlign: TextAlign.end,
                                  enabled: false,
                                  style: TextStyle(
                                      fontSize: 18,
                                      color: isDarkMode(context)
                                          ? Colors.white
                                          : Colors.black),
                                  cursorColor: const Color(COLOR_ACCENT),
                                  keyboardType: TextInputType.phone,
                                  decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: 'Phone Number'.tr(),
                                      contentPadding:
                                          const EdgeInsets.only(bottom: 2)),
                                ),
                              ),
                            ),
                          ],
                        ).toList()),
                  ),
                  Padding(
                      padding: const EdgeInsets.only(top: 32.0, bottom: 16),
                      child: ConstrainedBox(
                        constraints:
                            const BoxConstraints(minWidth: double.infinity),
                        child: Material(
                          elevation: 2,
                          color: isDarkMode(context)
                              ? Colors.black12
                              : Colors.white,
                          child: CupertinoButton(
                            padding: const EdgeInsets.all(12.0),
                            onPressed: () async {},
                            child: Text(
                              'Save',
                              style: TextStyle(
                                  fontSize: 18, color: Color(COLOR_PRIMARY)),
                            ).tr(),
                          ),
                        ),
                      )),
                ]),
          ),
        ));
  }

  @override
  void initState() {
    user = widget.user;
    super.initState();
  }

  _updateUser() async {
    user.firstName = firstName!;
    user.lastName = lastName!;
    user.email = email!;
    user.phoneNumber = mobile!;
    user.carNumber = carPlate!;
    user.carName = carName!;
    var updatedUser = await FireStoreUtils.updateCurrentUser(user);
    if (updatedUser != null) {
      MyAppState.currentUser = user;
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: const Text(
        'Details saved successfully',
        style: TextStyle(fontSize: 17),
      ).tr()));
    } else {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: const Text(
        "Couldn't save details, Please try again",
        style: TextStyle(fontSize: 17),
      ).tr()));
    }
  }
}
