import 'package:easy_localization/easy_localization.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/services/helper.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/material.dart';

class ResetPasswordScreen extends StatefulWidget {
  const ResetPasswordScreen({super.key});

  @override
  _ResetPasswordScreenState createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final GlobalKey<FormState> _chaveFormulario = GlobalKey();
  AutovalidateMode _modoValidacao = AutovalidateMode.disabled;
  String _enderecoEmail = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        iconTheme: IconThemeData(
            color: isDarkMode(context) ? Colors.white : Colors.black),
        elevation: 0.0,
      ),
      body: Form(
        autovalidateMode: _modoValidacao,
        key: _chaveFormulario,
        child: SingleChildScrollView(
          child: Column(
            children: [
              _construirTitulo(),
              _construirCampoEmail(),
              _construirBotaoEnviar(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _construirBotaoEnviar() {
    return Padding(
      padding: const EdgeInsets.only(right: 40.0, left: 40.0, top: 40),
      child: ConstrainedBox(
        constraints: const BoxConstraints(minWidth: double.infinity),
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Color(COLOR_PRIMARY),
            padding: const EdgeInsets.only(top: 12, bottom: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25.0),
              side: BorderSide(color: Color(COLOR_PRIMARY)),
            ),
          ),
          onPressed: _executarResetSenha,
          child: Text(
            'Send Link'.tr(),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: isDarkMode(context) ? Colors.black : Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _construirCampoEmail() {
    return ConstrainedBox(
      constraints: const BoxConstraints(minWidth: double.infinity),
      child: Padding(
        padding: const EdgeInsets.only(top: 32.0, right: 24.0, left: 24.0),
        child: TextFormField(
          textAlignVertical: TextAlignVertical.center,
          textInputAction: TextInputAction.done,
          validator: validateEmail,
          onFieldSubmitted: (_) => _executarResetSenha(),
          onSaved: (valor) => _enderecoEmail = valor!,
          style: const TextStyle(fontSize: 18.0),
          keyboardType: TextInputType.emailAddress,
          cursorColor: Color(COLOR_PRIMARY),
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.only(left: 16, right: 16),
            hintText: 'E-mail'.tr(),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(25.0),
              borderSide: BorderSide(color: Color(COLOR_PRIMARY), width: 2.0),
            ),
            errorBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: Theme.of(context).colorScheme.error),
              borderRadius: BorderRadius.circular(25.0),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: Theme.of(context).colorScheme.error),
              borderRadius: BorderRadius.circular(25.0),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.grey.shade200),
              borderRadius: BorderRadius.circular(25.0),
            ),
          ),
        ),
      ),
    );
  }

  Widget _construirTitulo() {
    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.only(top: 32.0, right: 16.0, left: 16.0),
        child: Text(
          'Reset Password',
          style: TextStyle(
              color: Color(COLOR_PRIMARY),
              fontSize: 25.0,
              fontWeight: FontWeight.bold),
        ).tr(),
      ),
    );
  }

  void _executarResetSenha() async {
    if (!(_chaveFormulario.currentState?.validate() ?? false)) {
      setState(() {
        _modoValidacao = AutovalidateMode.onUserInteraction;
      });
      return;
    }

    _chaveFormulario.currentState!.save();
    showProgress(context, 'Sending Email...'.tr(), false);

    try {
      await auth.FirebaseAuth.instance.sendPasswordResetEmail(
        email: _enderecoEmail,
      );

      hideProgress();
      if (mounted) {
        _exibirMensagemSucesso();
        Navigator.of(context).pop();
      }
    } on auth.FirebaseAuthException catch (excecaoFirebase) {
      hideProgress();
      if (mounted) {
        _tratarErroFirebase(excecaoFirebase.code);
      }
    } catch (_) {
      hideProgress();
      if (mounted) {
        _exibirMensagemErroGenerico();
      }
    }
  }

  void _exibirMensagemErroGenerico() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Error sending reset email. Please try again.'.tr()),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _exibirMensagemSucesso() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Please check your email.'.tr()),
        backgroundColor: Colors.green,
      ),
    );
  }

  String _obterMensagemErro(String codigoErro) {
    switch (codigoErro) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'too-many-requests':
        return 'Too many attempts. Please try again later.';
      default:
        return 'Error sending reset email. Please try again.';
    }
  }

  void _tratarErroFirebase(String codigoErro) {
    final mensagem = _obterMensagemErro(codigoErro);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensagem.tr()),
        backgroundColor: Colors.red,
      ),
    );
  }
}
