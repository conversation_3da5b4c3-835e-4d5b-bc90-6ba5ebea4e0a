import 'package:emartdriver/model/VendorModel.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

class LojasAssociadas extends StatefulWidget {
  const LojasAssociadas({super.key});

  @override
  State<LojasAssociadas> createState() => _LojasAssociadasState();
}

class VincularLojasPage extends StatefulWidget {
  const VincularLojasPage({super.key});

  @override
  State<VincularLojasPage> createState() => _VincularLojasPageState();
}

class _LojasAssociadasState extends State<LojasAssociadas> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<VendorModel>>(
      stream: FireStoreUtils.associatedStores(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _construirEstadoCarregamento();
        }

        if (snapshot.hasError) {
          return _construirEstadoErro("Erro ao carregar lojas");
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return _construirEstadoVazio();
        }

        return _construirListaLojas(snapshot.data!);
      },
    );
  }

  Widget _construirEstadoCarregamento() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xff6C63FF)),
          ),
          SizedBox(height: 16),
          Text(
            "Carregando lojas...",
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirEstadoErro(String mensagem) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            mensagem,
            style: const TextStyle(
              fontSize: 18,
              color: Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => setState(() {}),
            icon: const Icon(Icons.refresh),
            label: const Text("Tentar novamente"),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirEstadoVazio() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.store_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          const Text(
            "Nenhuma loja associada",
            style: TextStyle(
              fontSize: 20,
              color: Colors.grey,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            "Compartilhe seu código para vincular lojas",
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirListaLojas(List<VendorModel> lojas) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: lojas.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) => _construirCardLoja(lojas[index]),
    );
  }

  Widget _construirCardLoja(VendorModel loja) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [Colors.white, Colors.grey[50]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _processarAcaoLoja(loja),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                _construirIconeStatusLoja(loja.situation.title),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        loja.title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xff2D3748),
                        ),
                      ),
                      const SizedBox(height: 8),
                      _construirTextoAcao(loja.situation.title),
                    ],
                  ),
                ),
                _construirIconeAcao(loja.situation.title),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _construirIconeStatusLoja(String situacao) {
    Color corFundo;
    IconData icone;
    Color corIcone;

    switch (situacao) {
      case "Aceito":
        corFundo = Colors.green[100]!;
        icone = Icons.store;
        corIcone = Colors.green[700]!;
        break;
      case "Aguardando aceite":
        corFundo = Colors.orange[100]!;
        icone = Icons.pending;
        corIcone = Colors.orange[700]!;
        break;
      default:
        corFundo = Colors.red[100]!;
        icone = Icons.store_outlined;
        corIcone = Colors.red[700]!;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: corFundo,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        icone,
        color: corIcone,
        size: 24,
      ),
    );
  }

  Widget _construirTextoAcao(String situacao) {
    String texto;
    Color cor;

    switch (situacao) {
      case "Aceito":
        texto = "Toque para desvincular";
        cor = Colors.red[600]!;
        break;
      case "Aguardando aceite":
        texto = "Toque para aprovar";
        cor = Colors.green[600]!;
        break;
      default:
        texto = "Loja recusada";
        cor = Colors.red[600]!;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: cor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: cor.withOpacity(0.3)),
      ),
      child: Text(
        texto,
        style: TextStyle(
          color: cor,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _construirIconeAcao(String situacao) {
    IconData icone;
    Color cor;

    switch (situacao) {
      case "Aceito":
        icone = Icons.link_off;
        cor = Colors.red[600]!;
        break;
      case "Aguardando aceite":
        icone = Icons.check_circle;
        cor = Colors.green[600]!;
        break;
      default:
        icone = Icons.cancel;
        cor = Colors.red[600]!;
    }

    return Icon(icone, color: cor, size: 24);
  }

  Future<void> _processarAcaoLoja(VendorModel loja) async {
    if (loja.situation.title == "Aceito") {
      await _processarDesvinculacao(loja);
      return;
    }

    if (loja.situation.title == "Aguardando aceite") {
      await _processarAprovacao(loja);
    }
  }

  Future<void> _processarDesvinculacao(VendorModel loja) async {
    final confirmacao = await _exibirDialogoConfirmacao(
      titulo: "Desvincular loja",
      mensagem: "Deseja desvincular da loja ${loja.title}?",
      textoConfirmacao: "DESVINCULAR",
      corConfirmacao: Colors.red,
    );

    if (confirmacao == true) {
      await FireStoreUtils.removeAssociatedStore(loja.id);
    }
  }

  Future<void> _processarAprovacao(VendorModel loja) async {
    final confirmacao = await _exibirDialogoConfirmacao(
      titulo: "Vincular loja",
      mensagem: "Deseja vincular a loja ${loja.title}?",
      textoConfirmacao: "VINCULAR",
      corConfirmacao: Colors.green,
    );

    if (confirmacao == true) {
      await FireStoreUtils.approveStore(loja.id);
    }
  }

  Future<bool?> _exibirDialogoConfirmacao({
    required String titulo,
    required String mensagem,
    required String textoConfirmacao,
    required Color corConfirmacao,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Text(
          titulo,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Text(mensagem),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              "CANCELAR",
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: corConfirmacao,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(textoConfirmacao),
          ),
        ],
      ),
    );
  }
}

class _VincularLojasPageState extends State<VincularLojasPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _construirCardCodigo(),
                const SizedBox(height: 32),
                _construirTituloLojas(),
                const Expanded(
                  child: LojasAssociadas(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _construirCardCodigo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xff425799), Color.fromARGB(255, 123, 149, 235)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xff425799).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.qr_code,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                "Código de vinculo",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            "Compartilhe este código com as lojas para vinculação:",
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          _construirSecaoCodigo(),
        ],
      ),
    );
  }

  Widget _construirSecaoCodigo() {
    return FutureBuilder<String?>(
      future: FireStoreUtils.associateCode(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _construirCarregamentoCodigo();
        }

        if (snapshot.hasError) {
          return _construirErroCodigo();
        }

        if (snapshot.hasData) {
          return _construirCodigoSucesso(snapshot.data!);
        }

        return _construirCodigoIndisponivel();
      },
    );
  }

  Widget _construirCarregamentoCodigo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
          SizedBox(width: 12),
          Text(
            "Gerando código...",
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirErroCodigo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.error_outline, color: Colors.white, size: 20),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              "Erro ao gerar código",
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          IconButton(
            onPressed: () => setState(() {}),
            icon: const Icon(Icons.refresh, color: Colors.white, size: 20),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _construirCodigoSucesso(String codigo) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  "Código:",
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  codigo,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 2,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: () => _compartilharCodigo(codigo),
              icon: const Icon(Icons.share, color: Colors.white),
              tooltip: "Compartilhar código",
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirCodigoIndisponivel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.warning_outlined, color: Colors.white, size: 20),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              "Nenhum código disponível",
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          IconButton(
            onPressed: () => setState(() {}),
            icon: const Icon(Icons.refresh, color: Colors.white, size: 20),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _construirTituloLojas() {
    return Row(
      children: [
        const SizedBox(width: 15),
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xff425799).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.store,
            color: Color(0xff425799),
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        const Text(
          "Lojas Vinculadas",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xff2D3748),
          ),
        ),
      ],
    );
  }

  void _compartilharCodigo(String codigo) {
    Share.share(
      "Compartilhe este código com as lojas parceiras para as quais você presta serviço diretamente:\n\n$codigo",
      subject: "Código de vinculo - Delivery",
    );
  }
}
