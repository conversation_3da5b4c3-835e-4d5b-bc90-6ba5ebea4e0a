import 'dart:convert';

import 'package:emartdriver/constants.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:emartdriver/ui/vincular_ta_liso/vinculado_enum.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class VinculoTaEntregueApiService {
  final usuario = MyAppState.currentUser;

  Future<bool> cadastrarEntregador() async {
    final user = auth.FirebaseAuth.instance.currentUser!;

    final payload = {
      "nome": "${usuario!.lastName} ",
      "login": user.email,
      "email": usuario!.email,
      "celular": usuario!.phoneNumber,
      "tipo_veiculo": usuario!.carName,
      "id_remoto": usuario!.userID,
      "cpf": usuario!.cpf,
      "cnpj": usuario!.cnpj,
      "veiculo_placa": usuario!.carNumber,
      "veiculo_descricao": usuario!.carName,
      "foto": usuario!.profilePictureURL,
    };

    final url = Uri.parse(
        "https://desenv.taentregue.site/mobile/v1/entregador/gerenciar/cadastro-entregador.php");
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(payload),
      );
      solicitarReavaliacao();
      return jsonDecode(response.body)['success'];
    } catch (e) {
      debugPrint("Erro ao cadastrar entregador: $e");
      return false;
    }
  }

  Future<bool> solicitarReavaliacao() async {
    final user = auth.FirebaseAuth.instance.currentUser!;
    final firestore = FireStoreUtils.firestore;
    try {
      await firestore.collection(USERS).doc(user.uid).update({
        'ta_entregue_status': TaEntregueStatus.aguardando.index,
      });
      return true;
    } catch (e) {
      debugPrint("Erro ao solicitar reavaliação: $e");
      return false;
    }
  }

  Future<bool> verificarVinculo() async {
    final url = Uri.parse(
        "https://desenv.taentregue.site/mobile/v1/entregador/gerenciar/verificar-vinculo-entregador.php?id_remoto=${usuario!.userID}");
    try {
      final response = await http.get(url);
      debugPrint("Status Code: ${response.statusCode}");
      debugPrint("Response Body: ${response.body}");

      if (response.statusCode == 200) {
        return jsonDecode(response.body)['success'] == true;
      } else {
        return false;
      }
    } catch (e) {
      debugPrint("Erro ao verificar vínculo: $e");
      return false;
    }
  }
}
