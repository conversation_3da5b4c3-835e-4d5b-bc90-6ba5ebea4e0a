import 'package:emartdriver/model/User.dart';

class UsuarioValidador {
  static String obterEmailOuPadrao(User? usuario) {
    if (!usuarioEhValido(usuario)) {
      return 'Não informado';
    }
    return usuario!.email.isEmpty ? 'Não informado' : usuario.email;
  }

  static String obterNomeCompleto(User? usuario) {
    if (!usuarioEhValido(usuario)) {
      return 'Usuário não encontrado';
    }
    return '${usuario!.firstName} ${usuario.lastName}';
  }

  static String obterTelefoneOuPadrao(User? usuario) {
    if (!usuarioEhValido(usuario)) {
      return 'Não informado';
    }
    return usuario!.phoneNumber.isEmpty ? 'Não informado' : usuario.phoneNumber;
  }

  static String obterVeiculoOuPadrao(User? usuario) {
    if (!usuarioEhValido(usuario)) {
      return 'Não informado';
    }
    final nomeVeiculo = usuario!.carName;
    return nomeVeiculo.isEmpty ? 'Não informado' : nomeVeiculo;
  }

  static bool usuarioEhValido(User? usuario) {
    return usuario != null;
  }
}
