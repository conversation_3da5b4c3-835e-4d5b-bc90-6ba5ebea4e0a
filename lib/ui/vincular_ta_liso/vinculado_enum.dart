import 'package:flutter/material.dart';

enum TaEntregueStatus {
  aguardando(0, 'Aguardando', Colors.grey),
  aprovado(1, 'Aprovado', Colors.green),
  suspenso(2, 'Suspenso', Colors.orange),
  reprovado(3, 'Reprovado', Colors.red);

  final int value;
  final String label;
  final Color color;

  const TaEntregueStatus(this.value, this.label, this.color);

  static TaEntregueStatus fromValue(int? value) {
    if (value == null) {
      return TaEntregueStatus.aguardando;
    }

    return TaEntregueStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => TaEntregueStatus.aguardando,
    );
  }

  static Color getColorFromValue(int? value) {
    return TaEntregueStatus.fromValue(value).color;
  }
}
