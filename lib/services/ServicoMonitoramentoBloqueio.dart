import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/BloqueioEntregadorModel.dart';
import 'package:emartdriver/ui/bloqueio/TelaBloqueioEntregador.dart';
import 'package:flutter/material.dart';

typedef AcaoBloqueio = void Function(BloqueioEntregadorModel bloqueio);

class ServicoMonitoramentoBloqueio {
  static final ServicoMonitoramentoBloqueio _instancia =
      ServicoMonitoramentoBloqueio._interno();
  static const String _nomeColecao = 'block_delivery_man';
  static const String _campoEntregadorId = 'entregador_id';

  static const String _campoAtivo = 'active';
  StreamSubscription<QuerySnapshot>? _subscricaoStream;
  final StreamController<BloqueioEntregadorModel?> _controladorBloqueio =
      StreamController<BloqueioEntregadorModel?>.broadcast();

  AcaoBloqueio? _acaoQuandoBloqueado;
  AcaoBloqueio? _acaoQuandoDesbloqueado;

  factory ServicoMonitoramentoBloqueio() => _instancia;
  ServicoMonitoramentoBloqueio._interno();

  Stream<BloqueioEntregadorModel?> get streamBloqueio =>
      _controladorBloqueio.stream;

  void dispose() {
    _pararMonitoramento();
    _controladorBloqueio.close();
  }

  Future<bool> estaEntregadorBloqueado() async {
    final bloqueio = await verificarBloqueioAtual();
    return bloqueio?.ativo ?? false;
  }

  void iniciarMonitoramento({
    AcaoBloqueio? aoBloquear,
    AcaoBloqueio? aoDesbloquear,
  }) {
    final usuarioAtual = MyAppState.currentUser;
    if (usuarioAtual == null) {
      return;
    }

    _acaoQuandoBloqueado = aoBloquear;
    _acaoQuandoDesbloqueado = aoDesbloquear;

    _pararMonitoramento();
    _iniciarStreamFirestore(usuarioAtual.userID);

    _verificarBloqueioInicial();
  }

  void navegarParaTelaBloqueio(
      BuildContext context, BloqueioEntregadorModel bloqueio) {
    if (!context.mounted) return;

    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (_) => TelaBloqueioEntregador(bloqueio: bloqueio),
      ),
      (route) => false,
    );
  }

  Future<BloqueioEntregadorModel?> verificarBloqueioAtual() async {
    final usuarioAtual = MyAppState.currentUser;
    if (usuarioAtual == null) return null;

    try {
      final consulta = await FirebaseFirestore.instance
          .collection(_nomeColecao)
          .where(_campoEntregadorId, isEqualTo: usuarioAtual.userID)
          .where(_campoAtivo, isEqualTo: true)
          .limit(1)
          .get();

      if (consulta.docs.isEmpty) return null;

      final documento = consulta.docs.first;
      return BloqueioEntregadorModel.fromMap(
        documento.data(),
        documento.id,
      );
    } catch (erro) {
      return null;
    }
  }

  void _executarAcoesBloqueio(BloqueioEntregadorModel bloqueio) {
    if (bloqueio.ativo && _acaoQuandoBloqueado != null) {
      _acaoQuandoBloqueado!(bloqueio);
    } else if (!bloqueio.ativo && _acaoQuandoDesbloqueado != null) {
      _acaoQuandoDesbloqueado!(bloqueio);
    }
  }

  void _iniciarStreamFirestore(String idEntregador) {
    final colecaoFirestore =
        FirebaseFirestore.instance.collection(_nomeColecao);

    final consulta = colecaoFirestore
        .where(_campoEntregadorId, isEqualTo: idEntregador)
        .limit(1);

    _subscricaoStream = consulta.snapshots().listen(
      _processarMudancasFirestore,
      onError: (erro) {
        // Erro tratado silenciosamente
      },
    );
  }

  void _pararMonitoramento() {
    _subscricaoStream?.cancel();
    _subscricaoStream = null;
  }

  void _processarMudancasFirestore(QuerySnapshot snapshot) {
    if (snapshot.docs.isEmpty) {
      _controladorBloqueio.add(null);
      return;
    }

    final documentoBloqueio = snapshot.docs.first;
    final bloqueio = BloqueioEntregadorModel.fromMap(
      documentoBloqueio.data() as Map<String, dynamic>,
      documentoBloqueio.id,
    );

    _controladorBloqueio.add(bloqueio);
    _executarAcoesBloqueio(bloqueio);
  }

  Future<void> _verificarBloqueioInicial() async {
    try {
      final bloqueioAtual = await verificarBloqueioAtual();
      if (bloqueioAtual != null && bloqueioAtual.ativo) {
        _controladorBloqueio.add(bloqueioAtual);
        _executarAcoesBloqueio(bloqueioAtual);
      }
    } catch (erro) {
      // Erro tratado silenciosamente
    }
  }
}
