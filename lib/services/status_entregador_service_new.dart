import 'dart:async';
import 'dart:ui';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/firebase_options.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/services/background_notification_manager.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:geolocator/geolocator.dart';

const int _LOCATION_UPDATE_INTERVAL = 30;
const int _MAX_RETRY_ATTEMPTS = 3;
const int _RETRY_DELAY = 5;
const String _NOTIFICATION_CHANNEL_ID = 'status_entregador_channel';
const String _NOTIFICATION_CHANNEL_NAME = 'Status do Entregador';

void _log(String message) {
  if (kDebugMode) {
    print('[StatusEntregadorService] $message');
  }
}

Future<void> _updateLocation(
  StatusEntregadorService service,
  String userId,
  ServiceInstance serviceInstance,
  int retryCount,
  BackgroundNotificationManager notificationManager,
) async {
  try {
    if (!service.isOnline) {
      _log('Serviço offline, pulando atualização de localização');
      notificationManager.updateDeliveryStatus(
        isOnline: false,
        additionalInfo: 'Serviço pausado',
      );
      return;
    }

    _log('Obtendo localização atual...');
    notificationManager.updateInitializationStatus('Obtendo localização...');

    final position = await Geolocator.getCurrentPosition(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        timeLimit: Duration(seconds: 10),
      ),
    );

    await service.setOnlineStatus(
      true,
      latitude: position.latitude,
      longitude: position.longitude,
      userId: userId,
    );

    notificationManager.updateLocationInfo(
      latitude: position.latitude,
      longitude: position.longitude,
      accuracy: position.accuracy.toStringAsFixed(1),
    );

    _log('Localização atualizada: ${position.latitude}, ${position.longitude}');
  } catch (e) {
    _log('Erro ao obter localização: $e');

    if (retryCount >= _MAX_RETRY_ATTEMPTS) {
      _log('Máximo de tentativas atingido, parando serviço');
      notificationManager.updateError(
        errorType: 'Falha na localização',
        details: 'Máximo de tentativas atingido',
      );
      serviceInstance.stopSelf();
    } else {
      _log(
          'Erro temporário na localização (tentativa $retryCount/$_MAX_RETRY_ATTEMPTS)');
      notificationManager.updateError(
        errorType: 'Erro temporário',
        retryCount: retryCount,
        maxRetries: _MAX_RETRY_ATTEMPTS,
      );
      await Future.delayed(const Duration(seconds: _RETRY_DELAY));
    }
  }
}

@pragma('vm:entry-point')
void onStart(ServiceInstance service) async {
  try {
    DartPluginRegistrant.ensureInitialized();

    Timer? locationTimer;
    String? currentUserId;
    StatusEntregadorService? statusService;
    int retryCount = 0;

    final notificationManager = BackgroundNotificationManager();
    notificationManager.initialize(service);

    void stopService() {
      _log('Parando serviço...');
      locationTimer?.cancel();
      statusService?.dispose();
      notificationManager.dispose();
      service.stopSelf();
    }

    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      _log('Firebase inicializado com sucesso');
    } catch (e) {
      _log('Erro ao inicializar Firebase: $e');
      stopService();
      return;
    }

    statusService = StatusEntregadorService();

    service.on('stopService').listen((event) {
      _log('Comando de parada recebido');
      stopService();
    });

    service.on('startListening').listen((event) async {
      try {
        _log('Comando de início recebido: $event');

        if (event != null && event['userId'] != null) {
          currentUserId = event['userId'];
          _log('Iniciando monitoramento para usuário: $currentUserId');

          await statusService!.initialize(userId: currentUserId);

          notificationManager
              .updateInitializationStatus('Verificando permissões...');

          final permission = await Geolocator.checkPermission();
          if (permission == LocationPermission.denied) {
            final requestResult = await Geolocator.requestPermission();
            if (requestResult == LocationPermission.denied ||
                requestResult == LocationPermission.deniedForever) {
              _log('Permissão de localização negada');
              notificationManager.updatePermissionStatus(
                allGranted: false,
                missingPermissions: 'Localização',
              );
              stopService();
              return;
            }
          }

          notificationManager.updatePermissionStatus(allGranted: true);

          locationTimer = Timer.periodic(
            const Duration(seconds: _LOCATION_UPDATE_INTERVAL),
            (_) async {
              await _updateLocation(
                statusService!,
                currentUserId!,
                service,
                retryCount,
                notificationManager,
              );
            },
          );

          await _updateLocation(
            statusService,
            currentUserId!,
            service,
            retryCount,
            notificationManager,
          );

          notificationManager.updateDeliveryStatus(isOnline: true);
          _log('Serviço de monitoramento iniciado com sucesso');
        }
      } catch (e) {
        _log('Erro no listener de início: $e');
        notificationManager.updateError(
          errorType: 'Erro na inicialização',
          details: e.toString(),
        );
      }
    });

    service.on('setAsForeground').listen((event) {
      _log('Serviço definido como foreground');
    });

    _log('Serviço de background pronto');
  } catch (e) {
    _log('Erro crítico no onStart: $e');
  }
}

@pragma('vm:entry-point')
Future<bool> onIosBackground(ServiceInstance service) async {
  DartPluginRegistrant.ensureInitialized();
  return true;
}

Future<void> initializeBackgroundService() async {
  try {
    final service = FlutterBackgroundService();

    await service.configure(
      androidConfiguration: AndroidConfiguration(
        onStart: onStart,
        autoStart: false,
        isForegroundMode: true,
        notificationChannelId: _NOTIFICATION_CHANNEL_ID,
        initialNotificationTitle: 'Tá Liso - Entregador',
        initialNotificationContent: 'Serviço de rastreamento inicializado',
        foregroundServiceNotificationId: 888,
        autoStartOnBoot: false,
      ),
      iosConfiguration: IosConfiguration(
        autoStart: false,
        onForeground: onStart,
        onBackground: onIosBackground,
      ),
    );

    _log('Serviço de background configurado com sucesso');
  } catch (e) {
    _log('Erro ao configurar serviço de background: $e');
    rethrow;
  }
}

class StatusEntregadorService {
  static final StatusEntregadorService _instancia =
      StatusEntregadorService._interno();
  final CollectionReference _colecaoStatus =
      FirebaseFirestore.instance.collection('delivery_men_status');

  final List<void Function(bool)> _listeners = [];
  bool _isOnline = false;
  StreamSubscription<DocumentSnapshot>? _statusSubscription;

  factory StatusEntregadorService() {
    return _instancia;
  }

  StatusEntregadorService._interno();

  bool get isOnline => _isOnline;

  Future<void> initialize({String? userId}) async {
    final String id = userId ?? MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) {
      throw Exception('ID do usuário não disponível');
    }

    _statusSubscription?.cancel();
    _statusSubscription = _colecaoStatus.doc(id).snapshots().listen(
      (doc) {
        if (doc.exists) {
          final dados = doc.data() as Map<String, dynamic>?;
          final online = dados?['isOnline'] == true;
          if (_isOnline != online) {
            _isOnline = online;
            _notificarListeners(online);
          }
        }
      },
    );

    await _carregarStatusInicial(id);
  }

  Future<void> _carregarStatusInicial(String id) async {
    final doc = await _colecaoStatus.doc(id).get();
    final dados = doc.data() as Map<String, dynamic>?;
    _isOnline = dados?['isOnline'] == true;
    _notificarListeners(_isOnline);
  }

  void addListener(void Function(bool) listener) {
    _listeners.add(listener);
  }

  void removeListener(void Function(bool) listener) {
    _listeners.remove(listener);
  }

  void dispose() {
    _statusSubscription?.cancel();
    _listeners.clear();
  }

  Future<void> setOnlineStatus(
    bool online, {
    double? latitude,
    double? longitude,
    String? userId,
  }) async {
    final String id = userId ?? MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) return;

    final Map<String, dynamic> dados = {
      'entregador_id': id,
      'lastActive': Timestamp.now(),
      'isOnline': online,
      'updatedAt': Timestamp.now(),
    };

    if (latitude != null && longitude != null) {
      dados['location'] = {
        'latitude': latitude,
        'longitude': longitude,
      };
    }

    await _colecaoStatus.doc(id).set(dados, SetOptions(merge: true));

    if (_isOnline != online) {
      _isOnline = online;
      _notificarListeners(online);
    }
  }

  Future<Map<String, dynamic>?> obterStatus({String? idEntregador}) async {
    final String id = idEntregador ?? MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) {
      throw Exception('ID do entregador não disponível');
    }
    final doc = await _colecaoStatus.doc(id).get();
    return doc.data() as Map<String, dynamic>?;
  }

  void _notificarListeners(bool online) {
    for (final listener in _listeners) {
      listener(online);
    }
  }
}
