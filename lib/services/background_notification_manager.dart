import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_background_service/flutter_background_service.dart';

class BackgroundNotificationManager {
  static final BackgroundNotificationManager _instance =
      BackgroundNotificationManager._internal();
  factory BackgroundNotificationManager() => _instance;
  BackgroundNotificationManager._internal();

  ServiceInstance? _serviceInstance;
  String _currentTitle = 'Tá Liso - Entregador';
  String _currentContent = 'Serviço iniciado';

  // Constantes para notificação
  static const String _channelId = 'status_entregador_channel';
  static const String _channelName = 'Status do Entregador';
  static const int _notificationId = 888;

  // Função para log seguro
  void _log(String message) {
    if (kDebugMode) {
      print('[BackgroundNotificationManager] $message');
    }
  }

  /// Cria o canal de notificação para Android API >= 26
  void _createNotificationChannel() {
    if (Platform.isAndroid) {
      try {
        const MethodChannel channel =
            MethodChannel('flutter_background_service_android');
        channel.invokeMethod('createNotificationChannel', {
          'id': _channelId,
          'name': _channelName,
          'description': 'Notificações do serviço de status do entregador',
          'importance': 4, // IMPORTANCE_HIGH
        });
        _log('Canal de notificação criado: $_channelId');
      } catch (e) {
        _log('Erro ao criar canal de notificação: $e');
      }
    }
  }

  /// Inicializa o gerenciador com a instância do serviço
  void initialize(ServiceInstance serviceInstance) {
    _serviceInstance = serviceInstance;
    _createNotificationChannel();

    // Definir notificação inicial
    _updateNotification(
      title: _currentTitle,
      content: 'Serviço iniciado',
    );

    _log('Gerenciador de notificações inicializado');
  }

  /// Atualiza a notificação com status do entregador
  void updateDeliveryStatus({
    required bool isOnline,
    String? location,
    String? additionalInfo,
  }) {
    try {
      String status = isOnline ? 'Online' : 'Offline';
      String content = 'Status: $status';

      if (location != null && location.isNotEmpty) {
        content += ' | $location';
      }

      if (additionalInfo != null && additionalInfo.isNotEmpty) {
        content += ' | $additionalInfo';
      }

      _updateNotification(
        title: _currentTitle,
        content: content,
      );

      _log('Status atualizado: $content');
    } catch (e) {
      _log('Erro ao atualizar status do entregador: $e');
    }
  }

  /// Atualiza a notificação com informações de localização
  void updateLocationInfo({
    required double latitude,
    required double longitude,
    String? accuracy,
  }) {
    try {
      String locationText = 'Lat: ${latitude.toStringAsFixed(4)}, '
          'Lng: ${longitude.toStringAsFixed(4)}';

      if (accuracy != null) {
        locationText += ' (±${accuracy}m)';
      }

      _updateNotification(
        title: _currentTitle,
        content: 'Online | $locationText',
      );

      _log('Localização atualizada: $locationText');
    } catch (e) {
      _log('Erro ao atualizar localização: $e');
    }
  }

  /// Atualiza a notificação com erro
  void updateError({
    required String errorType,
    String? details,
    int? retryCount,
    int? maxRetries,
  }) {
    try {
      String content = 'Erro: $errorType';

      if (retryCount != null && maxRetries != null) {
        content += ' (tentativa $retryCount/$maxRetries)';
      }

      if (details != null && details.isNotEmpty) {
        content += ' - $details';
      }

      _updateNotification(
        title: _currentTitle,
        content: content,
      );

      _log('Erro reportado: $content');
    } catch (e) {
      _log('Erro ao atualizar notificação de erro: $e');
    }
  }

  /// Atualiza a notificação com status de inicialização
  void updateInitializationStatus(String status) {
    try {
      _updateNotification(
        title: _currentTitle,
        content: 'Inicializando: $status',
      );

      _log('Status de inicialização: $status');
    } catch (e) {
      _log('Erro ao atualizar status de inicialização: $e');
    }
  }

  /// Atualiza a notificação com informações de permissão
  void updatePermissionStatus({
    required bool allGranted,
    String? missingPermissions,
  }) {
    try {
      String content;
      if (allGranted) {
        content = 'Permissões: Todas concedidas';
      } else {
        content = 'Permissões: Pendentes';
        if (missingPermissions != null) {
          content += ' ($missingPermissions)';
        }
      }

      _updateNotification(
        title: _currentTitle,
        content: content,
      );

      _log('Status de permissões: $content');
    } catch (e) {
      _log('Erro ao atualizar status de permissões: $e');
    }
  }

  /// Atualiza a notificação com contagem de pedidos
  void updateOrderCount(int activeOrders) {
    try {
      String content = 'Online';
      if (activeOrders > 0) {
        content +=
            ' | $activeOrders pedido${activeOrders > 1 ? 's' : ''} ativo${activeOrders > 1 ? 's' : ''}';
      } else {
        content += ' | Aguardando pedidos';
      }

      _updateNotification(
        title: _currentTitle,
        content: content,
      );

      _log('Contagem de pedidos atualizada: $activeOrders');
    } catch (e) {
      _log('Erro ao atualizar contagem de pedidos: $e');
    }
  }

  /// Atualiza a notificação com tempo de atividade
  void updateUptime(Duration uptime) {
    try {
      String uptimeText;
      if (uptime.inHours > 0) {
        uptimeText = '${uptime.inHours}h ${uptime.inMinutes % 60}m';
      } else if (uptime.inMinutes > 0) {
        uptimeText = '${uptime.inMinutes}m';
      } else {
        uptimeText = '${uptime.inSeconds}s';
      }

      _updateNotification(
        title: _currentTitle,
        content: 'Online | Ativo há $uptimeText',
      );

      _log('Tempo de atividade atualizado: $uptimeText');
    } catch (e) {
      _log('Erro ao atualizar tempo de atividade: $e');
    }
  }

  /// Método interno para atualizar a notificação
  void _updateNotification({
    required String title,
    required String content,
  }) {
    if (_serviceInstance == null) {
      _log('Instância do serviço não inicializada');
      return;
    }

    try {
      // Atualizar cache local
      _currentTitle = title;
      _currentContent = content;

      // Tentar diferentes métodos de atualização baseado na versão do plugin
      _tryUpdateNotification(title, content);
    } catch (e) {
      _log('Erro ao atualizar notificação: $e');
    }
  }

  /// Tenta diferentes métodos para atualizar a notificação
  void _tryUpdateNotification(String title, String content) {
    bool sucesso = false;

    try {
      // Método 1: setForegroundNotificationInfo (mais recente)
      _serviceInstance!.invoke('setForegroundNotificationInfo', {
        'title': title,
        'content': content,
      });
      sucesso = true;
      _log('Notificação atualizada (método 1): $content');
      return;
    } catch (e) {
      _log('Método 1 falhou: $e');
    }

    try {
      // Método 2: setNotificationInfo (versão anterior)
      _serviceInstance!.invoke('setNotificationInfo', {
        'title': title,
        'content': content,
      });
      sucesso = true;
      _log('Notificação atualizada (método 2): $content');
      return;
    } catch (e) {
      _log('Método 2 falhou: $e');
    }

    try {
      // Método 3: updateNotification (fallback)
      _serviceInstance!.invoke('updateNotification', {
        'title': title,
        'content': content,
        'id': _notificationId,
      });
      sucesso = true;
      _log('Notificação atualizada (método 3): $content');
    } catch (e) {
      _log('Método 3 falhou: $e');
    }

    if (!sucesso) {
      _log('Falha ao atualizar notificação por todos os métodos');
    }
  }

  /// Limpa recursos
  void dispose() {
    _serviceInstance = null;
    _log('Gerenciador de notificações finalizado');
  }

  /// Getters para informações atuais
  String get currentTitle => _currentTitle;
  String get currentContent => _currentContent;
  bool get isInitialized => _serviceInstance != null;
}
