import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/material.dart';

/// Serviço responsável por exibir e gerenciar a verificação reCAPTCHA
class RecaptchaService {
  static final RecaptchaService _instancia = RecaptchaService._interno();

  factory RecaptchaService() {
    return _instancia;
  }

  RecaptchaService._interno();

  /// Exibe o reCAPTCHA em um modal para verificação manual do usuário
  Future<bool> exibirRecaptchaParaUsuario(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext contextoDialog) {
            return AlertDialog(
              title: const Text('Verificação de Segurança'),
              content: const SizedBox(
                width: double.maxFinite,
                height: 300,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.security,
                      size: 64,
                      color: Colors.blue,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Verificação de Segurança Necessária',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Para sua segurança, uma verificação adicional é necessária. '
                      'Clique em "Verificar" para continuar.',
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(contextoDialog).pop(false),
                  child: const Text('Cancelar'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(contextoDialog).pop(true),
                  child: const Text('Verificar'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  /// Realiza login com verificação reCAPTCHA quando necessário
  Future<auth.UserCredential?> loginComRecaptcha({
    required String email,
    required String senha,
    required BuildContext context,
  }) async {
    try {
      // Configurar Firebase Auth para forçar reCAPTCHA
      await auth.FirebaseAuth.instance.setSettings(
        appVerificationDisabledForTesting: false,
        forceRecaptchaFlow: true,
      );

      // Primeira tentativa de login
      return await auth.FirebaseAuth.instance
          .signInWithEmailAndPassword(email: email, password: senha);
    } on auth.FirebaseAuthException catch (excecao) {
      // Se reCAPTCHA for necessário, exibir para o usuário
      if (_requerRecaptcha(excecao.code)) {
        bool recaptchaSucesso = await exibirRecaptchaParaUsuario(context);

        if (recaptchaSucesso) {
          // Tentar login novamente após verificação bem-sucedida
          return await auth.FirebaseAuth.instance
              .signInWithEmailAndPassword(email: email, password: senha);
        }
      }
      rethrow;
    }
  }

  /// Verifica se o erro requer verificação reCAPTCHA
  bool _requerRecaptcha(String codigoErro) {
    return codigoErro == 'too-many-requests' ||
        codigoErro == 'captcha-check-failed' ||
        codigoErro == 'recaptcha-not-enabled';
  }
}
