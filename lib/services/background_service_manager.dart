import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_background_service/flutter_background_service.dart';

import '../main.dart';
import 'status_entregador_service.dart';

class BackgroundServiceManager {
  static final BackgroundServiceManager _instance =
      BackgroundServiceManager._internal();
  factory BackgroundServiceManager() => _instance;
  BackgroundServiceManager._internal();

  bool _isInitialized = false;
  bool _isRunning = false;
  Timer? _healthCheckTimer;

  void _log(String message) {
    if (kDebugMode) {
      print('[BackgroundServiceManager] $message');
    }
  }

  /// Inicializa o gerenciador de serviços de background
  Future<bool> initialize() async {
    if (_isInitialized) {
      _log('Serviço já foi inicializado');
      return true;
    }

    try {
      await initializeBackgroundService();
      _isInitialized = true;
      _log('Serviço inicializado com sucesso');

      // Iniciar monitoramento de saúde do serviço
      _startHealthCheck();

      return true;
    } catch (e) {
      _log('Erro ao inicializar serviço: $e');
      return false;
    }
  }

  /// Inicia o serviço para um usuário específico
  Future<bool> startServiceForUser(String userId) async {
    if (!_isInitialized) {
      _log('Serviço não foi inicializado ainda');
      return false;
    }

    try {
      final service = FlutterBackgroundService();

      // Verificar se o serviço está rodando
      bool isRunning = await service.isRunning();

      if (!isRunning) {
        _log('Iniciando serviço de background...');
        await service.startService();

        // Aguardar um pouco para o serviço inicializar
        await Future.delayed(const Duration(seconds: 2));

        // Verificar novamente se está rodando
        isRunning = await service.isRunning();
        if (!isRunning) {
          _log('Falha ao iniciar serviço de background');
          return false;
        }
      }

      _log('Enviando comando para iniciar escuta para usuário: $userId');
      service.invoke('startListening', {'userId': userId});

      _isRunning = true;
      _log('Serviço iniciado com sucesso para usuário: $userId');

      return true;
    } catch (e) {
      _log('Erro ao iniciar serviço para usuário $userId: $e');
      return false;
    }
  }

  /// Para o serviço de background
  Future<void> stopService() async {
    try {
      final service = FlutterBackgroundService();
      service.invoke('stopService');
      _isRunning = false;
      _log('Comando de parada enviado para o serviço');
    } catch (e) {
      _log('Erro ao parar serviço: $e');
    }
  }

  /// Verifica se o serviço está rodando
  Future<bool> isServiceRunning() async {
    if (!_isInitialized) {
      return false;
    }

    try {
      final service = FlutterBackgroundService();
      return await service.isRunning();
    } catch (e) {
      _log('Erro ao verificar status do serviço: $e');
      return false;
    }
  }

  /// Inicia monitoramento periódico da saúde do serviço
  void _startHealthCheck() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _performHealthCheck(),
    );
  }

  /// Realiza verificação de saúde do serviço
  Future<void> _performHealthCheck() async {
    try {
      final isRunning = await isServiceRunning();

      if (_isRunning && !isRunning) {
        _log('Serviço parou inesperadamente, tentando reiniciar...');

        if (MyAppState.currentUser?.userID != null) {
          await startServiceForUser(MyAppState.currentUser!.userID);
        }
      }
    } catch (e) {
      _log('Erro durante verificação de saúde: $e');
    }
  }

  /// Limpa recursos
  void dispose() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = null;
  }

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isRunning => _isRunning;
}
