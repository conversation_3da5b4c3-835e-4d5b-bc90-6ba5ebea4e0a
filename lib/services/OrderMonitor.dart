import 'dart:async';
import 'dart:developer';
import 'dart:math' as math;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class OrderMonitor {
  // Singleton pattern
  static final OrderMonitor _instance = OrderMonitor._internal();
  factory OrderMonitor() => _instance;
  OrderMonitor._internal();

  // Stream controllers
  StreamSubscription<QuerySnapshot>? _ordersSubscription;

  // Current orders cache
  final List<OrderModel> _currentOrders = [];

  // Callback for new orders
  Function(OrderModel)? onNewOrderAvailable;

  // Current position
  LatLng? _currentPosition;

  // Maximum distance to show orders (in)
  final double maxStoreDistanceInKM = 10.0;

  // Initialize the monitor
  void initialize({
    required Function(OrderModel) onNewOrder,
    required LatLng? currentPosition,
    String? currentUserId,
  }) {
    onNewOrderAvailable = onNewOrder;
    _currentPosition = currentPosition;

    // Start monitoring
    _startMonitoring();
  }

  // Update current position
  void updatePosition(LatLng position) {
    _currentPosition = position;
  }

  // Start monitoring for new orders
  void _startMonitoring() {
    _ordersSubscription?.cancel();

    try {
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('status', isEqualTo: OrderStatus.driverSearching.description);

      // First, get current orders
      query.get().then((snapshot) {
        _processInitialOrders(snapshot);

        // Then listen for changes
        _ordersSubscription = query.snapshots().listen((querySnapshot) {
          _processOrdersUpdate(querySnapshot);
        }, onError: (e) {
          log("Error listening to available orders: $e");
        });
      });
    } catch (e) {
      log("Error starting order monitoring: $e");
    }
  }

  // Process initial orders
  void _processInitialOrders(QuerySnapshot snapshot) {
    try {
      _currentOrders.clear();

      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final order = OrderModel.fromJson(data);

        // Add to current orders
        _currentOrders.add(order);
      }

      log("Initial orders loaded: ${_currentOrders.length}");
    } catch (e) {
      log("Error processing initial orders: $e");
    }
  }

  // Process orders update
  void _processOrdersUpdate(QuerySnapshot snapshot) {
    try {
      // Check for new orders
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final order = OrderModel.fromJson(data);

        // Check if this is a new order
        bool isNewOrder = !_currentOrders
            .any((existingOrder) => existingOrder.id == order.id);

        if (isNewOrder) {
          log("New order detected: ${order.id}, status: ${order.status}, entregador_id: ${order.entregador_id}");

          // Check if the order is within range
          if (_isOrderInRange(order)) {
            // Add to current orders
            _currentOrders.add(order);

            // Notify listener
            if (onNewOrderAvailable != null) {
              onNewOrderAvailable!(order);
            }
          }
        } else {
          // Verificar se um pedido existente foi aceito por outro entregador
          OrderModel existingOrder = _currentOrders.firstWhere(
              (existingOrder) => existingOrder.id == order.id,
              orElse: () => order);

          // Se o status mudou para aceito e o entregador_id foi preenchido
          if (existingOrder.status != OrderStatus.driverAccepted.description &&
              order.status == OrderStatus.driverAccepted.description &&
              order.entregador_id != null &&
              order.entregador_id!.isNotEmpty) {
            log("Pedido ${order.id} foi aceito por entregador: ${order.entregador_id}");

            // Atualizar o pedido na lista
            int index = _currentOrders.indexWhere((o) => o.id == order.id);
            if (index >= 0) {
              _currentOrders[index] = order;
            }

            // Notificar o listener sobre a mudança
            if (onNewOrderAvailable != null) {
              onNewOrderAvailable!(order);
            }
          }
        }
      }

      // Check for removed orders
      List<OrderModel> ordersToRemove = [];
      for (var existingOrder in _currentOrders) {
        bool orderStillExists =
            snapshot.docs.any((doc) => doc.id == existingOrder.id);
        if (!orderStillExists) {
          ordersToRemove.add(existingOrder);
        }
      }

      // Remove orders that no longer exist
      for (var orderToRemove in ordersToRemove) {
        _currentOrders.removeWhere((order) => order.id == orderToRemove.id);
      }
    } catch (e) {
      log("Error processing orders update: $e");
    }
  }

  // Check if an order is within range
  bool _isOrderInRange(OrderModel order) {
    if (_currentPosition == null) return false;

    LatLng? storePos =
        _getLatLng(order.vendor.address_store?.location.geoPoint);
    if (storePos == null) return false;

    // Calculate distance between driver and store
    double distanceInKm = _calculateDistance(_currentPosition!.latitude,
        _currentPosition!.longitude, storePos.latitude, storePos.longitude);

    // Only show stores within the configured distance limit
    return distanceInKm <= maxStoreDistanceInKM;
  }

  // Get LatLng from GeoPoint
  LatLng? _getLatLng(GeoPoint? location) {
    if (location == null) return null;
    return LatLng(location.latitude, location.longitude);
  }

  // Calculate distance between two coordinates in kilometers
  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Radius of the earth in
    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) *
            math.cos(_degreesToRadians(lat2)) *
            math.sin(dLon / 2) *
            math.sin(dLon / 2);

    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    double distance = earthRadius * c;

    return distance;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  // Dispose
  void dispose() {
    _ordersSubscription?.cancel();
    _currentOrders.clear();
    onNewOrderAvailable = null;
  }
}
