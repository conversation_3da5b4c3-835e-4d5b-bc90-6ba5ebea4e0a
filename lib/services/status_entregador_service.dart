import 'dart:async';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:emartdriver/main.dart';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Classes de dados
class SoundState {
  final bool isPlaying;
  final String? activatedBy;
  final String? activatedAt;
  final String? stoppedBy;
  final String? stoppedAt;

  SoundState({
    required this.isPlaying,
    this.activatedBy,
    this.activatedAt,
    this.stoppedBy,
    this.stoppedAt,
  });

  factory SoundState.fromJson(Map<String, dynamic> json) {
    return SoundState(
      isPlaying: json['is_playing'] ?? false,
      activatedBy: json['activated_by'],
      activatedAt: json['activated_at'],
      stoppedBy: json['stopped_by'],
      stoppedAt: json['stopped_at'],
    );
  }
}

class DeviceData {
  final String deviceId;
  final double latitude;
  final double longitude;
  final String timestamp;
  final String receivedAt;
  final String? nickname;

  DeviceData({
    required this.deviceId,
    required this.latitude,
    required this.longitude,
    required this.timestamp,
    required this.receivedAt,
    this.nickname,
  });

  factory DeviceData.fromJson(Map<String, dynamic> json) {
    return DeviceData(
      deviceId: json['device_id'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      timestamp: json['timestamp'],
      receivedAt: json['received_at'],
      nickname: json['nickname'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'device_id': deviceId,
      'latitude': latitude,
      'longitude': longitude,
      'timestamp': timestamp,
      'received_at': receivedAt,
      'nickname': nickname,
    };
  }
}

class StatusEntregadorService {
  static final StatusEntregadorService _instancia =
      StatusEntregadorService._interno();

  // Firebase collections
  final CollectionReference _colecaoStatus =
      FirebaseFirestore.instance.collection('delivery_men_status');

  // Constants
  static const String _isOnlineKey = 'is_online';
  static const String _deviceIdKey = 'device_id';
  static const String _nicknameKey = 'device_nickname';
  static const int _updateInterval = 10000; // 10 segundos para Firebase

  // State variables
  final List<void Function(bool)> _listeners = [];
  bool _isOnline = false;
  StreamSubscription<DocumentSnapshot>? _statusSubscription;

  // Location tracking
  static String? _cachedDeviceId;
  static String? _cachedNickname;
  static Timer? _locationTimer;

  // Callback para quando o estado do som mudar
  static Function(SoundState)? onSoundStateChanged;

  factory StatusEntregadorService() {
    return _instancia;
  }

  StatusEntregadorService._interno();

  bool get isOnline => _isOnline;

  // Inicialização do ID do dispositivo
  static Future<void> initDeviceId() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? savedId = prefs.getString(_deviceIdKey);

      if (savedId != null) {
        _cachedDeviceId = savedId;
        return;
      }

      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      String deviceId;

      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceId = 'android_${androidInfo.id}';
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceId = 'ios_${iosInfo.identifierForVendor}';
      } else {
        deviceId = 'device_${DateTime.now().millisecondsSinceEpoch}';
      }

      await prefs.setString(_deviceIdKey, deviceId);
      _cachedDeviceId = deviceId;
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao inicializar ID do dispositivo: $e');
      }
    }
  }

  // Verificação de permissões
  static Future<bool> checkPermissions() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return false;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever) {
      return false;
    }

    return true;
  }

  static Future<void> requestPermissions() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      await Geolocator.requestPermission();
    }
  }

  // Obter localização atual
  static Future<Position?> getCurrentLocation() async {
    try {
      bool hasPermission = await checkPermissions();
      if (!hasPermission) {
        return null;
      }

      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );
      return position;
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao obter localização: $e');
      }
      return null;
    }
  }

  // Obter ID do dispositivo
  static Future<String> getDeviceId() async {
    if (_cachedDeviceId != null) {
      return _cachedDeviceId!;
    }

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? savedId = prefs.getString(_deviceIdKey);

      if (savedId != null) {
        _cachedDeviceId = savedId;
        return savedId;
      }

      // Gerar um ID único baseado no timestamp para evitar conflitos
      String fallbackId =
          'device_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecondsSinceEpoch}';
      _cachedDeviceId = fallbackId;
      return fallbackId;
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao obter ID do dispositivo no background: $e');
      }
      // Gerar ID único mesmo em caso de erro
      String errorId = 'device_error_${DateTime.now().millisecondsSinceEpoch}';
      _cachedDeviceId = errorId;
      return errorId;
    }
  }

  // Gerenciar nickname
  static Future<String?> getNickname() async {
    if (_cachedNickname != null) {
      return _cachedNickname;
    }

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? nickname = prefs.getString(_nicknameKey);
      _cachedNickname = nickname;
      return nickname;
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao obter nickname: $e');
      }
      return null;
    }
  }

  static Future<void> setNickname(String nickname) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_nicknameKey, nickname);
      _cachedNickname = nickname;
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao salvar nickname: $e');
      }
    }
  }

  // Métodos principais da classe
  Future<void> initialize() async {
    final String id = MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) return;

    // Inicializar device ID se necessário
    await initDeviceId();

    _statusSubscription?.cancel();
    _statusSubscription = _colecaoStatus.doc(id).snapshots().listen((doc) {
      final dados = doc.data() as Map<String, dynamic>?;
      final online = dados?['isOnline'] == true;
      _isOnline = online;
      _notificarListeners(online);
    });
    final doc = await _colecaoStatus.doc(id).get();
    final dados = doc.data() as Map<String, dynamic>?;
    _isOnline = dados?['isOnline'] == true;
  }

  void addListener(void Function(bool) listener) {
    _listeners.add(listener);
  }

  void removeListener(void Function(bool) listener) {
    _listeners.remove(listener);
  }

  void dispose() {
    _statusSubscription?.cancel();
    _listeners.clear();
  }

  Future<void> setOnlineStatus(bool online,
      {double? latitude, double? longitude}) async {
    final String id = MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) return;

    final Map<String, dynamic> dados = {
      'entregador_id': id,
      'lastActive': Timestamp.now(),
      'isOnline': online,
      'updatedAt': Timestamp.now(),
    };

    if (latitude != null && longitude != null) {
      dados['location'] = {
        'latitude': latitude,
        'longitude': longitude,
      };
    }

    await _colecaoStatus.doc(id).set(dados, SetOptions(merge: true));
    _isOnline = online;
    _notificarListeners(online);

    // Gerenciar atualizações periódicas de localização
    if (online) {
      await startPeriodicLocationUpdates();
    } else {
      stopPeriodicLocationUpdates();
    }
  }

  Future<Map<String, dynamic>?> obterStatus({String? idEntregador}) async {
    final String id = idEntregador ?? MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) {
      throw Exception('ID do entregador não disponível');
    }
    final doc = await _colecaoStatus.doc(id).get();
    return doc.data() as Map<String, dynamic>?;
  }

  void _notificarListeners(bool online) {
    for (final listener in _listeners) {
      listener(online);
    }
  }

  // Métodos de localização periódica
  static Future<void> startPeriodicLocationUpdates() async {
    // Para qualquer timer existente
    _locationTimer?.cancel();

    // Inicia novo timer para enviar localização a cada 10 segundos
    _locationTimer = Timer.periodic(
        const Duration(milliseconds: _updateInterval), (timer) async {
      try {
        Position? position = await getCurrentLocation();
        if (position != null) {
          String deviceId = await getDeviceId();
          await _sendLocationToFirebase(
              deviceId, position.latitude, position.longitude);
          if (kDebugMode) {
            print(
                'Localização enviada periodicamente: ${position.latitude}, ${position.longitude}');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('Erro no envio periódico de localização: $e');
        }
      }
    });

    if (kDebugMode) {
      print(
          'Iniciado envio periódico de localização a cada ${_updateInterval / 1000} segundos');
    }
  }

  static void stopPeriodicLocationUpdates() {
    _locationTimer?.cancel();
    _locationTimer = null;
    if (kDebugMode) {
      print('Parado envio periódico de localização');
    }
  }

  // Enviar localização para Firebase
  static Future<void> _sendLocationToFirebase(
      String deviceId, double latitude, double longitude) async {
    try {
      final String userId = MyAppState.currentUser?.userID ?? '';
      if (userId.isEmpty) return;

      final String? nickname = await getNickname();

      final Map<String, dynamic> locationData = {
        'device_id': deviceId,
        'user_id': userId,
        'latitude': latitude,
        'longitude': longitude,
        'timestamp': Timestamp.now(),
        'nickname': nickname,
      };

      // Salvar na coleção de localizações
      await FirebaseFirestore.instance
          .collection('delivery_men_locations')
          .doc(userId)
          .set(locationData, SetOptions(merge: true));

      // Atualizar também o status principal
      await FirebaseFirestore.instance
          .collection('delivery_men_status')
          .doc(userId)
          .update({
        'location': {
          'latitude': latitude,
          'longitude': longitude,
        },
        'lastActive': Timestamp.now(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao enviar localização para Firebase: $e');
      }
    }
  }

  // Métodos de status online/offline
  static Future<void> setOnlineStatusStatic(bool isOnline) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isOnlineKey, isOnline);
  }

  static Future<bool> getOnlineStatusStatic() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isOnlineKey) ?? false;
  }

  // Buscar todos os dispositivos conectados (para compatibilidade com o documento)
  static Future<List<DeviceData>?> getAllDevices() async {
    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('delivery_men_locations')
          .get();

      List<DeviceData> devices = [];
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        devices.add(DeviceData(
          deviceId: data['device_id'] ?? '',
          latitude: (data['latitude'] ?? 0.0).toDouble(),
          longitude: (data['longitude'] ?? 0.0).toDouble(),
          timestamp: data['timestamp']?.toDate().toIso8601String() ?? '',
          receivedAt: data['timestamp']?.toDate().toString() ?? '',
          nickname: data['nickname'],
        ));
      }
      return devices;
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao buscar dispositivos: $e');
      }
      return null;
    }
  }

  // Funcionalidades de som (placeholder para compatibilidade)
  static Future<bool> stopSound() async {
    try {
      // Implementar lógica de parar som se necessário
      if (kDebugMode) {
        print('Som parado com sucesso');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao parar som: $e');
      }
      return false;
    }
  }

  static Future<SoundState?> getSoundState() async {
    try {
      // Implementar lógica de verificar estado do som se necessário
      return SoundState(isPlaying: false);
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao verificar estado do som: $e');
      }
      return null;
    }
  }
}
