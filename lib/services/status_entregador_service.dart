import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:permission_handler/permission_handler.dart';

/// Serviço para gerenciar o status online/offline do entregador
/// e rastreamento de localização em tempo real
class StatusEntregadorService {
  static final StatusEntregadorService _instancia =
      StatusEntregadorService._internal();

  // Estado interno
  bool _isOnline = false;
  bool _isInitialized = false;
  String? _userId;
  String? _deviceId;

  // Listeners para mudanças de status
  final List<Function(bool)> _listeners = [];

  // Timer para atualizações periódicas de localização
  Timer? _locationTimer;

  // Singleton
  factory StatusEntregadorService() {
    return _instancia;
  }

  StatusEntregadorService._internal();

  /// Inicializa o serviço
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Gera um ID único para o dispositivo
      _deviceId = _generateDeviceId();

      // Solicita permissões de localização
      await _requestLocationPermissions();

      _isInitialized = true;
      if (kDebugMode) {
        print('StatusEntregadorService inicializado com sucesso');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao inicializar StatusEntregadorService: $e');
      }
      rethrow;
    }
  }

  /// Gera um ID único para o dispositivo
  String _generateDeviceId() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNum = random.nextInt(999999);
    return 'device_${timestamp}_$randomNum';
  }

  /// Solicita permissões de localização
  Future<void> _requestLocationPermissions() async {
    try {
      // Verifica se o serviço de localização está habilitado
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Serviço de localização desabilitado');
      }

      // Solicita permissão de localização
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Permissão de localização negada');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Permissão de localização negada permanentemente');
      }

      // Solicita permissão de localização em background (Android)
      if (await Permission.locationAlways.isDenied) {
        await Permission.locationAlways.request();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao solicitar permissões de localização: $e');
      }
      rethrow;
    }
  }

  /// Define o status online/offline do entregador
  Future<void> setOnlineStatus(bool isOnline) async {
    if (!_isInitialized) {
      throw Exception('Serviço não inicializado. Chame initialize() primeiro.');
    }

    try {
      _isOnline = isOnline;

      if (isOnline) {
        await _startLocationTracking();
      } else {
        await _stopLocationTracking();
      }

      // Atualiza o status no Firebase
      await _updateStatusInFirebase(isOnline);

      // Notifica os listeners
      _notifyListeners(isOnline);

      if (kDebugMode) {
        print(
            'Status do entregador alterado para: ${isOnline ? "Online" : "Offline"}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erro ao definir status online: $e');
      }
      rethrow;
    }
  }

  /// Inicia o rastreamento de localização
  Future<void> _startLocationTracking() async {
    // Para o timer anterior se existir
    _locationTimer?.cancel();

    // Inicia um novo timer para enviar localização a cada 10 segundos
    _locationTimer = Timer.periodic(const Duration(seconds: 10), (timer) async {
      try {
        await _sendLocationToFirebase();
      } catch (e) {
        if (kDebugMode) {
          print('Erro ao enviar localização: $e');
        }
      }
    });

    // Envia a localização imediatamente
    await _sendLocationToFirebase();
  }

  /// Para o rastreamento de localização
  Future<void> _stopLocationTracking() async {
    _locationTimer?.cancel();
    _locationTimer = null;
  }

  /// Envia a localização atual para o Firebase
  Future<void> _sendLocationToFirebase() async {
    if (!_isOnline) return;

    try {
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.bestForNavigation,
          distanceFilter: 10,
        ),
      ).timeout(const Duration(seconds: 15));

      final now = DateTime.now();
      final locationData = {
        'device_id': _deviceId,
        'latitude': position.latitude,
        'longitude': position.longitude,
        'timestamp': now.toIso8601String(),
        'received_at': FieldValue.serverTimestamp(),
        'accuracy': position.accuracy,
        'speed': position.speed,
        'heading': position.heading,
      };

      // Tenta salvar na coleção de localizações com timeout
      const String apiUrl = 'https://pool.taliso.com.br/geotest.php';

      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'device_id': deviceId,
          'latitude': position.latitude,
          'longitude': position.longitude,
          'timestamp': DateTime.now().toIso8601String(),
          'nickname': 'Jose',
        }),
      );

      if (kDebugMode) {
        print(
            'Localização enviada: ${position.latitude}, ${position.longitude}');
      }
    } on LocationServiceDisabledException {
      if (kDebugMode) {
        print('Serviço de localização desabilitado');
      }
    } on PermissionDeniedException {
      if (kDebugMode) {
        print('Permissão de localização negada');
      }
    } catch (e) {
      if (kDebugMode) {
        if (e.toString().contains('firestore.googleapis.com') ||
            e.toString().contains('UNAVAILABLE') ||
            e.toString().contains('UnknownHostException')) {
          print('Erro de conectividade com Firebase - sem internet: $e');
        } else {
          print('Erro ao enviar localização para Firebase: $e');
        }
      }
    }
  }

  /// Atualiza o status no Firebase
  Future<void> _updateStatusInFirebase(bool isOnline) async {
    if (_userId == null) return;

    try {
      await FirebaseFirestore.instance
          .collection('drivers')
          .doc(_userId)
          .update({
        'isOnline': isOnline,
        'lastSeen': FieldValue.serverTimestamp(),
        'device_id': _deviceId,
      }).timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          throw Exception('Timeout de conectividade');
        },
      );
    } catch (e) {
      if (kDebugMode) {
        if (e.toString().contains('firestore.googleapis.com') ||
            e.toString().contains('UNAVAILABLE') ||
            e.toString().contains('UnknownHostException')) {
          print('Erro de conectividade com Firebase - sem internet: $e');
        } else {
          print('Erro ao atualizar status no Firebase: $e');
        }
      }
    }
  }

  /// Adiciona um listener para mudanças de status
  void addListener(Function(bool) listener) {
    _listeners.add(listener);
  }

  /// Remove um listener
  void removeListener(Function(bool) listener) {
    _listeners.remove(listener);
  }

  /// Notifica todos os listeners sobre mudança de status
  void _notifyListeners(bool isOnline) {
    for (var listener in _listeners) {
      try {
        listener(isOnline);
      } catch (e) {
        if (kDebugMode) {
          print('Erro ao notificar listener: $e');
        }
      }
    }
  }

  /// Define o ID do usuário
  void setUserId(String userId) {
    _userId = userId;
  }

  /// Retorna se o entregador está online
  bool get isOnline => _isOnline;

  /// Retorna se o serviço foi inicializado
  bool get isInitialized => _isInitialized;

  /// Retorna o ID do dispositivo
  String? get deviceId => _deviceId;

  /// Limpa recursos e para todos os timers
  void dispose() {
    _locationTimer?.cancel();
    _locationTimer = null;
    _listeners.clear();
    _isOnline = false;
    _isInitialized = false;
  }
}
