import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/main.dart';

class StatusEntregadorService {
  static final StatusEntregadorService _instancia =
      StatusEntregadorService._interno();
  final CollectionReference _colecaoStatus =
      FirebaseFirestore.instance.collection('delivery_men_status');

  final List<void Function(bool)> _listeners = [];
  bool _isOnline = false;
  StreamSubscription<DocumentSnapshot>? _statusSubscription;

  factory StatusEntregadorService() {
    return _instancia;
  }

  StatusEntregadorService._interno();

  bool get isOnline => _isOnline;

  Future<void> initialize() async {
    final String id = MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) return;
    _statusSubscription?.cancel();
    _statusSubscription = _colecaoStatus.doc(id).snapshots().listen((doc) {
      final dados = doc.data() as Map<String, dynamic>?;
      final online = dados?['isOnline'] == true;
      _isOnline = online;
      _notificarListeners(online);
    });
    final doc = await _colecaoStatus.doc(id).get();
    final dados = doc.data() as Map<String, dynamic>?;
    _isOnline = dados?['isOnline'] == true;
  }

  void addListener(void Function(bool) listener) {
    _listeners.add(listener);
  }

  void removeListener(void Function(bool) listener) {
    _listeners.remove(listener);
  }

  void dispose() {
    _statusSubscription?.cancel();
    _listeners.clear();
  }

  Future<void> setOnlineStatus(bool online,
      {double? latitude, double? longitude}) async {
    final String id = MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) return;
    final Map<String, dynamic> dados = {
      'entregador_id': id,
      'lastActive': Timestamp.now(),
      'isOnline': online,
      'updatedAt': Timestamp.now(),
    };
    if (latitude != null && longitude != null) {
      dados['location'] = {
        'latitude': latitude,
        'longitude': longitude,
      };
    }
    await _colecaoStatus.doc(id).set(dados, SetOptions(merge: true));
    _isOnline = online;
    _notificarListeners(online);
  }

  Future<Map<String, dynamic>?> obterStatus({String? idEntregador}) async {
    final String id = idEntregador ?? MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) {
      throw Exception('ID do entregador não disponível');
    }
    final doc = await _colecaoStatus.doc(id).get();
    return doc.data() as Map<String, dynamic>?;
  }

  void _notificarListeners(bool online) {
    for (final listener in _listeners) {
      listener(online);
    }
  }
}
