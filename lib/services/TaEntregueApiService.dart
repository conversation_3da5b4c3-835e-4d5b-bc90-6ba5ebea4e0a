import 'dart:convert';

import 'package:emartdriver/model/WalletTransactionModel.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class TaEntregueApiService {
  static const String _baseUrl =
      'https://desenv.taentregue.site/mobile/v1/entregador';

  static Future<Map<String, dynamic>> gerarCodigoEntregador(
      String celular) async {
    const String token =
        'd9d55febfc8656cc41174fe2377d29b0242ebcca05b2d326ea92afa7c3b2c7df';
    const String apiUrl =
        'https://gis.starnorte.com.br/mobile/v2/api/entregador/gerar-codigo.php?token=$token';

    try {
      final url = Uri.parse(apiUrl);
      final response = await http.post(url,
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({'celular': celular}));
      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        if (result['codigo_gerado'] != null) {
          return {
            'success': true,
            'codigo_gerado': result['codigo_gerado'],
          };
        }
      }
      return {
        'success': false,
        'message':
            'Ocorreu um erro inesperado. A equipe de desenvolvimento está analisando.'
      };
    } catch (e) {
      debugPrint('Erro ao gerar código: $e');
      return {
        'success': false,
        'message':
            'Ocorreu um erro inesperado. A equipe de desenvolvimento está analisando.'
      };
    }
  }

  static Future<List<WalletTransactionModel>> getTransactionHistory({
    required String idRemoto,
  }) async {
    try {
      final url = Uri.parse(
          '$_baseUrl/wallet/historico_transacoes.php?id_entregador=$idRemoto');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = jsonDecode(response.body);
        return jsonList
            .map((json) => WalletTransactionModel.fromJson(json))
            .toList();
      } else {
        debugPrint('Erro ao buscar histórico: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      debugPrint('Erro ao buscar histórico: $e');
      return [];
    }
  }

  static Future<Map<String, dynamic>> getWalletBalance({
    required String idRemoto,
  }) async {
    try {
      final url = Uri.parse('$_baseUrl/wallet/saldo.php?id_remoto=$idRemoto');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        return result;
      } else {
        return {
          'success': false,
          'message':
              'Ocorreu um erro inesperado. A equipe de desenvolvimento está analisando.'
        };
      }
    } catch (e) {
      debugPrint('Erro ao consultar saldo: $e');
      return {
        'success': false,
        'message':
            'Ocorreu um erro inesperado. A equipe de desenvolvimento está analisando.'
      };
    }
  }

  static Future<Map<String, dynamic>> requestWithdrawal({
    required String idRemoto,
  }) async {
    try {
      final requestUrl =
          Uri.parse('$_baseUrl/saque/solicitacao.php?id_remoto=$idRemoto');
      final response = await http.get(requestUrl);
      if (response.statusCode == 20) {
        final result = jsonDecode(response.body);
        return result;
      } else {
        return {
          'status': 'error',
          'message':
              'Ocorreu um erro inesperado. A equipe de desenvolvimento está analisando.'
        };
      }
    } catch (e) {
      debugPrint('Erro ao solicitar saque: $e');
      return {
        'status': 'error',
        'message':
            'Ocorreu um erro inesperado. A equipe de desenvolvimento está analisando.'
      };
    }
  }
}
