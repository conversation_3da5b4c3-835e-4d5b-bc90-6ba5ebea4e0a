import 'package:emartdriver/model/BloqueioEntregadorModel.dart';
import 'package:emartdriver/services/ServicoMonitoramentoBloqueio.dart';
import 'package:flutter/material.dart';

class GerenciadorMonitoramentoBloqueio extends StatefulWidget {
  final Widget child;

  const GerenciadorMonitoramentoBloqueio({
    super.key,
    required this.child,
  });

  @override
  State<GerenciadorMonitoramentoBloqueio> createState() =>
      _GerenciadorMonitoramentoBloqueioState();
}

class _GerenciadorMonitoramentoBloqueioState
    extends State<GerenciadorMonitoramentoBloqueio> {
  final ServicoMonitoramentoBloqueio _servicoMonitoramento =
      ServicoMonitoramentoBloqueio();

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  @override
  void dispose() {
    _servicoMonitoramento.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _inicializarMonitoramento();
  }

  void _inicializarMonitoramento() {
    _servicoMonitoramento.iniciarMonitoramento(
      aoBloquear: _processarBloqueio,
      aoDesbloquear: _processarDesbloqueio,
    );
  }

  void _processarBloqueio(BloqueioEntregadorModel bloqueio) {
    if (mounted && context.mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          _servicoMonitoramento.navegarParaTelaBloqueio(context, bloqueio);
        }
      });
    }
  }

  void _processarDesbloqueio(BloqueioEntregadorModel bloqueio) {
    // Ação opcional quando o entregador for desbloqueado
  }
}
