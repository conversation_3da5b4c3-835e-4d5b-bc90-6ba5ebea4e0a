import 'package:emartdriver/model/TaxModel.dart';
import 'package:emartdriver/model/mail_setting.dart';
import 'package:location/location.dart';
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';

import 'model/CurrencyModel.dart';

const cabAccepted = "cab_accepted";

const cabCompleted = "cab_completed";
const CARMAKES = 'car_make';
const CARMODEL = 'car_model';
const CATEGORIES = 'vendor_categories';
const COLOR_ACCENT = 0xFF8fd468;
const COLOR_ACCENt1 = 0xFF94D5BE;
const COLOR_PRIMARY_DARK = 0xFF2c7305;

const CONTACT_US = 'ContactUs';
const Currency = 'currencies';
const DARK_CARD_BG_COLOR = 0xff242528; // 0xFF5EA23A;
const DARK_COLOR = 0xff191A1C;
const DARK_VIEWBG_COLOR = 0xff191A1C;
const DEFAULT_CAR_IMAGE =
    'https://firebasestorage.googleapis.com/v0/b/Tá entregue-8d99f.appspot.com/o/images%2Fcar_default_image.png?alt=media&token=ba12a79d-d876-4b1c-87ed-2b06cd5b50f0';
const driverAccepted = "driver_accepted";
const driverCompleted = "driver_completed";
const driverPayouts = "driver_payouts";
const dynamicNotification = 'dynamic_notification';
const emailTemplates = 'email_templates';

// 0xFF5EA23A;
const FACEBOOK_BUTTON_COLOR = 0xFF415893;

const GlobalURL = "https://emartadmin.siswebapp.com/";
const HOUR_MILLIS = 60 * MINUTE_MILLIS;
const MINUTE_MILLIS = 60 * SECOND_MILLIS;
const newCarBook = "new_car_book";
const newOrderPlaced = "new_order_placed";
const newVendorSignup = "new_vendor_signup";
const Order_Rating = 'items_review';
const ORDERS = 'vendor_orders';
const OrderTransaction = "order_transactions";
const parcelAccepted = "parcel_accepted";
const parcelCompleted = "parcel_completed";
const PARCELORDER = "parcel_orders";
const parcelRejected = "parcel_rejected";
const payoutRequest = "payout_request";
const payoutRequestStatus = "payout_request_status";

const PRODUCTS = 'vendor_products';
const REFERRAL = 'referral';
const rentalAccepted = "rental_accepted";
const rentalCompleted = "rental_completed";

const RENTALORDER = "rental_orders";
const rentalRejected = "rental_rejected";
const RENTALVEHICLETYPE = 'rental_vehicle_type';

const REPORTS = 'reports';

const RIDESORDER = "rides";
const SECOND_MILLIS = 1000;
const SECTION = 'sections';
const Setting = 'settings';
const startRide = "start_ride";
const STORAGE_ROOT = 'Tá entregue';
const USER_ROLE_DRIVER = 'driver';
const USERS = 'users';
const VEHICLETYPE = 'vehicle_type';
const VENDORS = 'vendors';
const Wallet = "wallet";

const walletTopup = "wallet_topup";
const withdrawMethod = 'withdraw_method';
var COLOR_PRIMARY = 0xFF00B761;
CurrencyModel? currencyData;
String currentCabOrderID = "";
int driverOrderAcceptRejectDuration = 60;

bool enableOTPParcelReceive = false;

bool enableOTPTripStart = false;

String GOOGLE_API_KEY = 'AIzaSyD6Cry2XHdEycsrOW1othM2LbNIGEk5TyA';

String jsonNotificationFileURL = '';
LocationData? locationDataFinal;
MailSettings? mailSettings;

// Maximum distance in kilometers to show stores to drivers
// Orders can be for any distance, but only stores within this radius will be shown
double maxStoreDistanceInKM = 4.0;
String minimumAmountToWithdrawal = "0.0";

String minimumDepositToRideAccept = "0.0";
String placeholderImage =
    'https://firebasestorage.googleapis.com/v0/b/Tá entregue-8d99f.appspot.com/o/images%2Fplace_holder%20(2).png?alt=media&token=c2eb35a9-ddf2-4b66-9cc6-d7d82e48d97b';

String? selectedMapType = "osm";

String senderId = '';

final smtpServer = SmtpServer(mailSettings!.host.toString(),
    username: mailSettings!.userName.toString(),
    password: mailSettings!.password.toString(),
    port: 465,
    ignoreBadCertificate: false,
    ssl: true,
    allowInsecure: true);

String amountShow({required String? amount}) {
  if (currencyData != null) {
    if (currencyData!.symbolatright == true) {
      return "${double.parse(amount.toString()).toStringAsFixed(currencyData!.decimal)} ${currencyData!.symbol.toString()}";
    } else {
      return "${currencyData!.symbol.toString()} ${double.parse(amount.toString()).toStringAsFixed(currencyData!.decimal)}";
    }
  } else {
    return "\$ ${double.parse(amount.toString()).toStringAsFixed(2)}";
  }
}

double calculateTax({String? amount, TaxModel? taxModel}) {
  double taxAmount = 0.0;
  if (taxModel != null && taxModel.enable == true) {
    if (taxModel.type == "fix") {
      taxAmount = double.parse(taxModel.tax.toString());
    } else {
      taxAmount = (double.parse(amount.toString()) *
              double.parse(taxModel.tax!.toString())) /
          100;
    }
  }
  return taxAmount;
}

sendMail(
    {String? subject,
    String? body,
    bool? isAdmin = false,
    List<dynamic>? recipients}) async {
  // Create our message.
  if (isAdmin == true) {
    recipients!.add(mailSettings!.userName.toString());
  }
  final message = Message()
    ..from = Address(
        mailSettings!.userName.toString(), mailSettings!.fromName.toString())
    ..recipients = recipients!
    ..subject = subject
    ..text = body
    ..html = body;

  try {
    final sendReport = await send(message, smtpServer);
    print('Message sent: $sendReport');
  } on MailerException catch (e) {
    print(e);
    print('Message not sent.');
    for (var p in e.problems) {
      print('Problem: ${p.code}: ${p.msg}');
    }
  }
}
